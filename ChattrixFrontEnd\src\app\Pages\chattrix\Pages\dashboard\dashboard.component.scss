.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  gap: 20px;

  .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    color: white;
    font-size: 1.2rem;
    margin: 0;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.dashboard-content {
  width: 100%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.welcome-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;

  .card-header {
    padding: 24px;
    text-align: center;
    border-bottom: 1px solid #eee;

    .user-avatar {
      font-size: 48px;
      margin-bottom: 16px;
    }

    h1 {
      color: #333;
      font-size: 1.8rem;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    h2 {
      color: #666;
      font-size: 1.1rem;
      font-weight: 400;
      margin: 0;
    }
  }

  .card-content {
    padding: 24px;

    .welcome-message {
      font-size: 1rem;
      color: #555;
      line-height: 1.6;
      margin-bottom: 24px;
      text-align: center;
    }

    .user-info {
      h3 {
        color: #333;
        margin-bottom: 16px;
        font-size: 1.3rem;
        font-weight: 500;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
      }

      .info-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #667eea;

        .info-icon {
          font-size: 20px;
        }

        .info-label {
          font-weight: 500;
          color: #333;
          min-width: 80px;
        }

        .info-value {
          color: #555;
          flex: 1;
        }
      }
    }
  }

  .card-actions {
    padding: 16px 24px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 12px;
    justify-content: center;
  }
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.feature-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .card-header {
    padding: 24px 24px 16px 24px;
    text-align: center;

    .feature-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    h3 {
      color: #333;
      font-size: 1.3rem;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    .subtitle {
      color: #999;
      font-style: italic;
      margin: 0;
    }
  }

  .card-content {
    padding: 0 24px 24px 24px;

    p {
      color: #666;
      line-height: 1.5;
      margin: 0;
      text-align: center;
    }
  }
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover {
      background: #5a6fd8;
      transform: translateY(-2px);
    }
  }

  &.btn-danger {
    background: #dc3545;
    color: white;

    &:hover {
      background: #c82333;
      transform: translateY(-2px);
    }
  }
}

.not-authenticated {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  width: 100%;
}

.error-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
  padding: 40px;

  .error-content {
    text-align: center;

    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    h2 {
      color: #333;
      margin-bottom: 16px;
      font-size: 1.5rem;
    }

    p {
      color: #666;
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .dashboard-content {
    gap: 20px;
  }

  .welcome-card {
    .card-header h1 {
      font-size: 1.5rem;
    }

    .user-info .info-grid {
      grid-template-columns: 1fr;
    }
  }

  .feature-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

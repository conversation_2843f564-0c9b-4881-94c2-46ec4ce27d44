<div class="dashboard-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner">Loading...</div>
    <p class="loading-text">Loading dashboard...</p>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading && isAuthenticated" class="dashboard-content">
    <!-- Header Section -->
    <div class="welcome-card">
      <div class="card-header">
        <div class="user-avatar">👤</div>
        <h1>Welcome to Chattrix!</h1>
        <h2>Hello, {{ userDisplayName }}</h2>
      </div>
      <div class="card-content">
        <p class="welcome-message">
          ✅ You have successfully logged into Chattrix. Your authentication is
          working properly.
        </p>

        <!-- User Information -->
        <div class="user-info" *ngIf="user">
          <h3>Your Account Information</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-icon">📧</span>
              <span class="info-label">Email:</span>
              <span class="info-value">{{
                user.email || "Not available"
              }}</span>
            </div>
            <div class="info-item" *ngIf="user.name">
              <span class="info-icon">👤</span>
              <span class="info-label">Name:</span>
              <span class="info-value">{{ user.name }}</span>
            </div>
            <div class="info-item">
              <span class="info-icon">🔒</span>
              <span class="info-label">Role:</span>
              <span class="info-value">{{ userRoleDisplay }}</span>
            </div>
            <div class="info-item" *ngIf="user.isActive !== undefined">
              <span class="info-icon">✅</span>
              <span class="info-label">Status:</span>
              <span class="info-value">{{
                user.isActive ? "Active" : "Inactive"
              }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="card-actions">
        <button class="btn btn-primary" routerLink="/dashboard/users">
          👥 User Management
        </button>
        <button class="btn btn-danger" (click)="onLogout()">🚪 Logout</button>
      </div>
    </div>

    <!-- Feature Cards -->
    <div class="feature-cards">
      <div class="feature-card" routerLink="/dashboard/users">
        <div class="card-header">
          <div class="feature-icon">👥</div>
          <h3>User Management</h3>
          <p class="subtitle">Manage Users</p>
        </div>
        <div class="card-content">
          <p>View and manage user accounts, roles, and permissions.</p>
        </div>
      </div>

      <div class="feature-card">
        <div class="card-header">
          <div class="feature-icon">💬</div>
          <h3>Chat Features</h3>
          <p class="subtitle">Coming Soon</p>
        </div>
        <div class="card-content">
          <p>
            Real-time messaging and chat functionality will be available here.
          </p>
        </div>
      </div>

      <div class="feature-card">
        <div class="card-header">
          <div class="feature-icon">⚙️</div>
          <h3>Settings</h3>
          <p class="subtitle">Coming Soon</p>
        </div>
        <div class="card-content">
          <p>
            User preferences and application settings will be available here.
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Not Authenticated State -->
  <div *ngIf="!isLoading && !isAuthenticated" class="not-authenticated">
    <div class="error-card">
      <div class="error-content">
        <div class="error-icon">❌</div>
        <h2>Access Denied</h2>
        <p>You need to be logged in to access the dashboard.</p>
        <p>Redirecting to login...</p>
      </div>
    </div>
  </div>
</div>

# PowerShell script to create and apply chat database migration

Write-Host "Creating Chat Database Migration..." -ForegroundColor Green

# Navigate to the project directory
Set-Location -Path "ChattrixBackend.EntityFramworkCore"

# Add migration
dotnet ef migrations add AddChatEntities --startup-project ../ChattrixBackend

# Update database
dotnet ef database update --startup-project ../ChattrixBackend

Write-Host "Chat database migration completed successfully!" -ForegroundColor Green

# Navigate back to root
Set-Location -Path ".."

{"version": 3, "sources": ["src/app/Pages/authentication/Pages/forget-password/forget-password.component.scss"], "sourcesContent": ["/* Forgot Password Component Specific Styles */\r\n\r\n.full-width {\r\n  width: 100%;\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n.reset-button {\r\n  height: 44px;\r\n  font-size: var(--font-size-base);\r\n  font-weight: 500;\r\n  text-transform: none;\r\n  border-radius: var(--radius-md);\r\n  margin-bottom: var(--spacing-md);\r\n  position: relative;\r\n\r\n  &:disabled {\r\n    opacity: 0.6;\r\n  }\r\n\r\n  .button-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: var(--spacing-sm);\r\n    position: relative;\r\n  }\r\n\r\n  .hidden {\r\n    visibility: hidden;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.button-spinner {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* Back to Login */\r\n.back-to-login {\r\n  text-align: center;\r\n  padding-top: var(--spacing-lg);\r\n}\r\n\r\n.back-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-xs);\r\n  color: var(--accent-green) !important;\r\n  text-transform: none;\r\n  font-size: var(--font-size-sm);\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    color: var(--accent-green-hover) !important;\r\n    background: transparent;\r\n  }\r\n}\r\n\r\n/* Logo Fallback Styles */\r\n.logo-fallback {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--accent-green) 0%,\r\n    var(--accent-green-light) 100%\r\n  );\r\n  border-radius: 50%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* Material UI Form Field Customizations */\r\n::ng-deep .mat-mdc-form-field {\r\n  .mat-mdc-text-field-wrapper {\r\n    background-color: var(--bg-input);\r\n    border-radius: var(--radius-md);\r\n  }\r\n\r\n  .mat-mdc-form-field-label {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .mat-mdc-form-field-input {\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  .mat-mdc-form-field-icon-suffix {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  &.mat-focused {\r\n    .mat-mdc-form-field-label {\r\n      color: var(--accent-green);\r\n    }\r\n  }\r\n}\r\n\r\n/* Snackbar Styles */\r\n::ng-deep .error-snackbar {\r\n  background-color: var(--error) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n::ng-deep .success-snackbar {\r\n  background-color: var(--success) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 480px) {\r\n  .auth-card {\r\n    margin: var(--spacing-sm);\r\n    max-width: calc(100vw - 2rem);\r\n  }\r\n\r\n  .auth-header {\r\n    padding: var(--spacing-lg) var(--spacing-md);\r\n  }\r\n\r\n  .auth-form {\r\n    padding: var(--spacing-lg) var(--spacing-md);\r\n  }\r\n\r\n  .auth-footer {\r\n    padding: var(--spacing-md);\r\n  }\r\n\r\n  .auth-logo {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .auth-title {\r\n    font-size: var(--font-size-lg);\r\n  }\r\n\r\n  .auth-subtitle {\r\n    font-size: var(--font-size-xs);\r\n  }\r\n}\r\n\r\n/* Focus and Hover States */\r\n.mat-mdc-button:focus {\r\n  outline: 2px solid var(--accent-green);\r\n  outline-offset: 2px;\r\n}\r\n\r\n.mat-mdc-form-field:focus-within {\r\n  .mat-mdc-text-field-wrapper {\r\n    border: 2px solid var(--accent-green);\r\n  }\r\n}\r\n\r\n/* Loading State Animation */\r\n.button-spinner {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n"], "mappings": ";AAEA,CAAA;AACE,SAAA;AACA,iBAAA,IAAA;;AAGF,CAAA;AACE,UAAA;AACA,aAAA,IAAA;AACA,eAAA;AACA,kBAAA;AACA,iBAAA,IAAA;AACA,iBAAA,IAAA;AACA,YAAA;;AAEA,CATF,YASE;AACE,WAAA;;AAGF,CAbF,aAaE,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,YAAA;;AAGF,CArBF,aAqBE,CAAA;AACE,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,YAAA;AACA,QAAA;AACA,OAAA;AACA,aAAA,UAAA,IAAA,EAAA;;AAIF,CAAA;AACE,cAAA;AACA,eAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA,IAAA;AACA,SAAA,IAAA;AACA,kBAAA;AACA,aAAA,IAAA;AACA,UAAA,EAAA;;AAEA,CATF,WASE;AACE,SAAA,IAAA;AACA,cAAA;;AAKJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA;IAAA;MAAA,MAAA;MAAA,IAAA,gBAAA,EAAA;MAAA,IAAA,sBAAA;AAKA,iBAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,eAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAKA,UAAA,CAAA,mBAAA,CAAA;AACE,oBAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,UAAA,CALA,mBAKA,CAAA;AACE,SAAA,IAAA;;AAGF,UAAA,CATA,mBASA,CAAA;AACE,SAAA,IAAA;;AAGF,UAAA,CAbA,mBAaA,CAAA;AACE,SAAA,IAAA;;AAIA,UAAA,CAlBF,kBAkBE,CAAA,YAAA,CAbF;AAcI,SAAA,IAAA;;AAMN,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAGF,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAIF,OAAA,CAAA,SAAA,EAAA;AACE,GAAA;AACE,YAAA,IAAA;AACA,eAAA,KAAA,MAAA,EAAA;;AAGF,GAAA;AACE,aAAA,IAAA,cAAA,IAAA;;AAGF,GAAA;AACE,aAAA,IAAA,cAAA,IAAA;;AAGF,GAAA;AACE,aAAA,IAAA;;AAGF,GAAA;AACE,WAAA;AACA,YAAA;;AAGF,GAAA;AACE,eAAA,IAAA;;AAGF,GAAA;AACE,eAAA,IAAA;;;AAKJ,CAAA,cAAA;AACE,WAAA,IAAA,MAAA,IAAA;AACA,kBAAA;;AAIA,CA3EA,kBA2EA,cAAA,CA3EA;AA4EE,UAAA,IAAA,MAAA,IAAA;;AAKJ,CArIA;AAsIE,aAAA,KAAA,GAAA,OAAA;;AAGF,WAHE;AAIA;AACE,eAAA,OAAA;;AAEF;AACE,eAAA,OAAA;;;", "names": []}
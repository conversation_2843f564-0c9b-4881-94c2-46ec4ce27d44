using AutoMapper;
using ChattrixBackend.Core.Entities.ChatManagement.ConversationModel;
using ChattrixBackend.Core.Entities.ChatManagement.MessageModel;
using ChattrixBackend.Core.Entities.ChatManagement.PresenceModel;
using ChattrixBackend.Core.Entities.ChatManagement.TypingModel;
using ChattrixBackend.Core.Entities.UserManagement.ResponseModel;
using ChattrixBackend.EntityFramworkCore.Data;
using ChattrixBackend.Services.S3Services;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ChattrixBackend.Services.ChatServices {
    public class ChatService : IChatService {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<ChatService> _logger;
        private readonly IS3Service _s3Service;

        public ChatService(
            ApplicationDbContext context,
            IMapper mapper,
            ILogger<ChatService> logger,
            IS3Service s3Service) {
            _context = context;
            _mapper = mapper;
            _logger = logger;
            _s3Service = s3Service;
        }

        public async Task<Response> SendMessageAsync(string senderId, string conversationId, string content, string messageType = "text", string? replyToMessageId = null, string? clientMessageId = null) {
            try {
                // Verify user is participant in conversation
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == senderId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                // Verify conversation exists and is active
                var conversation = await _context.Conversations
                    .FirstOrDefaultAsync(c => c.Id == conversationId && c.IsActive);

                if (conversation == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Conversation not found"
                    };
                }

                // Create message
                var message = new Message {
                    ConversationId = conversationId,
                    SenderId = senderId,
                    Content = content,
                    Type = Enum.Parse<MessageType>(messageType, true),
                    ReplyToMessageId = replyToMessageId,
                    ClientMessageId = clientMessageId
                };

                _context.Messages.Add(message);

                // Update conversation last message
                conversation.LastMessageId = message.Id;
                conversation.LastMessageAt = message.SentAt;
                conversation.UpdatedAt = DateTime.UtcNow;

                // Create message status for all participants except sender
                var participants = await _context.ConversationParticipants
                    .Where(cp => cp.ConversationId == conversationId && cp.IsActive && cp.UserId != senderId)
                    .ToListAsync();

                foreach (var p in participants) {
                    _context.MessageStatuses.Add(new MessageStatus {
                        MessageId = message.Id,
                        UserId = p.UserId,
                        Status = DeliveryStatus.Sent
                    });
                }

                await _context.SaveChangesAsync();

                // Get sender details for response
                var sender = await _context.Users.FindAsync(senderId);
                var messageResponse = new {
                    messageId = message.Id,
                    conversationId = message.ConversationId,
                    senderId = message.SenderId,
                    senderName = sender?.FullName ?? "Unknown",
                    content = message.Content,
                    type = message.Type.ToString().ToLower(),
                    sentAt = message.SentAt,
                    replyToMessageId = message.ReplyToMessageId,
                    clientMessageId = message.ClientMessageId
                };

                return new Response {
                    IsSuccess = true,
                    Message = "Message sent successfully",
                    Data = messageResponse
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error sending message from user {senderId} to conversation {conversationId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error sending message"
                };
            }
        }

        public async Task<Response> CreatePrivateConversationAsync(string userId1, string userId2) {
            try {
                // Check if conversation already exists
                var existingConversation = await _context.ConversationParticipants
                    .Where(cp => cp.UserId == userId1 || cp.UserId == userId2)
                    .GroupBy(cp => cp.ConversationId)
                    .Where(g => g.Count() == 2)
                    .Select(g => g.Key)
                    .FirstOrDefaultAsync();

                if (existingConversation != null) {
                    var existing = await _context.Conversations
                        .Include(c => c.Participants)
                        .ThenInclude(p => p.User)
                        .FirstOrDefaultAsync(c => c.Id == existingConversation && c.Type == ConversationType.Private);

                    if (existing != null) {
                        return new Response {
                            IsSuccess = true,
                            Message = "Conversation already exists",
                            Data = new {
                                conversationId = existing.Id,
                                name = existing.Name,
                                type = existing.Type.ToString().ToLower(),
                                participants = existing.Participants.Select(p => new {
                                    userId = p.UserId,
                                    name = p.User.FullName,
                                    role = p.Role.ToString().ToLower()
                                })
                            }
                        };
                    }
                }

                // Create new private conversation
                var user1 = await _context.Users.FindAsync(userId1);
                var user2 = await _context.Users.FindAsync(userId2);

                if (user1 == null || user2 == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "One or both users not found"
                    };
                }

                var conversation = new Conversation {
                    Name = $"{user1.FullName}, {user2.FullName}",
                    Type = ConversationType.Private,
                    CreatedBy = userId1
                };

                _context.Conversations.Add(conversation);

                // Add participants
                _context.ConversationParticipants.AddRange(new[] {
                    new ConversationParticipant {
                        ConversationId = conversation.Id,
                        UserId = userId1,
                        Role = ParticipantRole.Member
                    },
                    new ConversationParticipant {
                        ConversationId = conversation.Id,
                        UserId = userId2,
                        Role = ParticipantRole.Member
                    }
                });

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Private conversation created successfully",
                    Data = new {
                        conversationId = conversation.Id,
                        name = conversation.Name,
                        type = conversation.Type.ToString().ToLower(),
                        createdAt = conversation.CreatedAt
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error creating private conversation between {userId1} and {userId2}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error creating conversation"
                };
            }
        }

        public async Task<Response> CreateGroupConversationAsync(string creatorId, string name, string? description = null, List<string>? participantIds = null) {
            try {
                var conversation = new Conversation {
                    Name = name,
                    Description = description,
                    Type = ConversationType.Group,
                    CreatedBy = creatorId
                };

                _context.Conversations.Add(conversation);

                // Add creator as owner
                _context.ConversationParticipants.Add(new ConversationParticipant {
                    ConversationId = conversation.Id,
                    UserId = creatorId,
                    Role = ParticipantRole.Owner
                });

                // Add other participants
                if (participantIds != null && participantIds.Any()) {
                    foreach (var participantId in participantIds.Where(id => id != creatorId)) {
                        _context.ConversationParticipants.Add(new ConversationParticipant {
                            ConversationId = conversation.Id,
                            UserId = participantId,
                            Role = ParticipantRole.Member
                        });
                    }
                }

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Group conversation created successfully",
                    Data = new {
                        conversationId = conversation.Id,
                        name = conversation.Name,
                        description = conversation.Description,
                        type = conversation.Type.ToString().ToLower(),
                        createdAt = conversation.CreatedAt
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error creating group conversation by user {creatorId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error creating group conversation"
                };
            }
        }

        public async Task<Response> GetConversationsAsync(string userId, int page = 1, int pageSize = 20) {
            try {
                var conversations = await _context.ConversationParticipants
                    .Where(cp => cp.UserId == userId && cp.IsActive)
                    .Include(cp => cp.Conversation)
                    .ThenInclude(c => c.LastMessage)
                    .ThenInclude(m => m.Sender)
                    .Include(cp => cp.Conversation)
                    .ThenInclude(c => c.Participants)
                    .ThenInclude(p => p.User)
                    .OrderByDescending(cp => cp.Conversation.LastMessageAt ?? cp.Conversation.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(cp => new {
                        conversationId = cp.Conversation.Id,
                        name = cp.Conversation.Name,
                        description = cp.Conversation.Description,
                        type = cp.Conversation.Type.ToString().ToLower(),
                        lastMessage = cp.Conversation.LastMessage != null ? new {
                            content = cp.Conversation.LastMessage.Content,
                            senderName = cp.Conversation.LastMessage.Sender.FullName,
                            sentAt = cp.Conversation.LastMessage.SentAt
                        } : null,
                        participantCount = cp.Conversation.Participants.Count(p => p.IsActive),
                        unreadCount = _context.MessageStatuses
                            .Count(ms => ms.UserId == userId &&
                                   ms.Message.ConversationId == cp.ConversationId &&
                                   ms.Status != DeliveryStatus.Read),
                        lastActivity = cp.Conversation.LastMessageAt ?? cp.Conversation.CreatedAt
                    })
                    .ToListAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Conversations retrieved successfully",
                    Data = conversations
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error getting conversations for user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving conversations"
                };
            }
        }

        public async Task<Response> GetMessagesAsync(string conversationId, string userId, int page = 1, int pageSize = 50) {
            try {
                // Verify user is participant
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                var messages = await _context.Messages
                    .Where(m => m.ConversationId == conversationId && !m.IsDeleted)
                    .Include(m => m.Sender)
                    .Include(m => m.ReplyToMessage)
                    .ThenInclude(rm => rm.Sender)
                    .OrderByDescending(m => m.SentAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(m => new {
                        messageId = m.Id,
                        content = m.Content,
                        type = m.Type.ToString().ToLower(),
                        senderId = m.SenderId,
                        senderName = m.Sender.FullName,
                        sentAt = m.SentAt,
                        editedAt = m.EditedAt,
                        replyTo = m.ReplyToMessage != null ? new {
                            messageId = m.ReplyToMessage.Id,
                            content = m.ReplyToMessage.Content,
                            senderName = m.ReplyToMessage.Sender.FullName
                        } : null,
                        fileUrl = m.FileUrl,
                        fileName = m.FileName
                    })
                    .ToListAsync();

                // Reverse to get chronological order
                messages.Reverse();

                return new Response {
                    IsSuccess = true,
                    Message = "Messages retrieved successfully",
                    Data = messages
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error getting messages for conversation {conversationId} and user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving messages"
                };
            }
        }

        public async Task<Response> EditMessageAsync(string messageId, string userId, string newContent) {
            try {
                var message = await _context.Messages
                    .FirstOrDefaultAsync(m => m.Id == messageId && m.SenderId == userId && !m.IsDeleted);

                if (message == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Message not found or you don't have permission to edit it"
                    };
                }

                message.Content = newContent;
                message.EditedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Message edited successfully",
                    Data = new {
                        messageId = message.Id,
                        content = message.Content,
                        editedAt = message.EditedAt
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error editing message {messageId} by user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error editing message"
                };
            }
        }

        public async Task<Response> DeleteMessageAsync(string messageId, string userId) {
            try {
                var message = await _context.Messages
                    .FirstOrDefaultAsync(m => m.Id == messageId && m.SenderId == userId && !m.IsDeleted);

                if (message == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Message not found or you don't have permission to delete it"
                    };
                }

                message.IsDeleted = true;
                message.DeletedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Message deleted successfully"
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error deleting message {messageId} by user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error deleting message"
                };
            }
        }

        public async Task<Response> GetConversationAsync(string conversationId, string userId) {
            try {
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                var conversation = await _context.Conversations
                    .Include(c => c.Participants)
                    .ThenInclude(p => p.User)
                    .FirstOrDefaultAsync(c => c.Id == conversationId);

                if (conversation == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Conversation not found"
                    };
                }

                var conversationData = new {
                    conversationId = conversation.Id,
                    name = conversation.Name,
                    description = conversation.Description,
                    type = conversation.Type.ToString().ToLower(),
                    createdAt = conversation.CreatedAt,
                    participants = conversation.Participants
                        .Where(p => p.IsActive)
                        .Select(p => new {
                            userId = p.UserId,
                            name = p.User.FullName,
                            role = p.Role.ToString().ToLower(),
                            joinedAt = p.JoinedAt,
                            isOnline = p.User.IsOnline,
                            lastSeen = p.User.LastSeen
                        })
                };

                return new Response {
                    IsSuccess = true,
                    Message = "Conversation retrieved successfully",
                    Data = conversationData
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error getting conversation {conversationId} for user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving conversation"
                };
            }
        }

        public async Task<Response> AddParticipantAsync(string conversationId, string adminId, string participantId) {
            try {
                // Verify admin has permission
                var adminParticipant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == adminId &&
                                             cp.IsActive && (cp.Role == ParticipantRole.Admin || cp.Role == ParticipantRole.Owner));

                if (adminParticipant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You don't have permission to add participants"
                    };
                }

                // Check if user is already a participant
                var existingParticipant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == participantId);

                if (existingParticipant != null) {
                    if (existingParticipant.IsActive) {
                        return new Response {
                            IsSuccess = false,
                            Message = "User is already a participant"
                        };
                    }
                    else {
                        // Reactivate participant
                        existingParticipant.IsActive = true;
                        existingParticipant.JoinedAt = DateTime.UtcNow;
                        existingParticipant.LeftAt = null;
                    }
                }
                else {
                    // Add new participant
                    _context.ConversationParticipants.Add(new ConversationParticipant {
                        ConversationId = conversationId,
                        UserId = participantId,
                        Role = ParticipantRole.Member
                    });
                }

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Participant added successfully"
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error adding participant {participantId} to conversation {conversationId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error adding participant"
                };
            }
        }

        public async Task<Response> LeaveConversationAsync(string conversationId, string userId) {
            try {
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                participant.IsActive = false;
                participant.LeftAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Left conversation successfully"
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error leaving conversation {conversationId} for user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error leaving conversation"
                };
            }
        }

        public async Task<Response> UpdateMessageStatusAsync(string messageId, string userId, string status) {
            try {
                var messageStatus = await _context.MessageStatuses
                    .FirstOrDefaultAsync(ms => ms.MessageId == messageId && ms.UserId == userId);

                if (messageStatus == null) {
                    // Create new status if it doesn't exist
                    messageStatus = new MessageStatus {
                        MessageId = messageId,
                        UserId = userId,
                        Status = Enum.Parse<DeliveryStatus>(status, true)
                    };
                    _context.MessageStatuses.Add(messageStatus);
                }
                else {
                    // Update existing status
                    messageStatus.Status = Enum.Parse<DeliveryStatus>(status, true);
                    messageStatus.StatusAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Message status updated successfully"
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error updating message status for message {messageId} and user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error updating message status"
                };
            }
        }

        public async Task<Response> MarkMessagesAsReadAsync(string conversationId, string userId, List<string> messageIds) {
            try {
                // Verify user is participant in conversation
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                // Update message statuses to read
                var messageStatuses = await _context.MessageStatuses
                    .Where(ms => messageIds.Contains(ms.MessageId) && ms.UserId == userId)
                    .ToListAsync();

                foreach (var status in messageStatuses) {
                    status.Status = DeliveryStatus.Read;
                    status.StatusAt = DateTime.UtcNow;
                }

                // Create read status for messages that don't have status records
                var existingMessageIds = messageStatuses.Select(ms => ms.MessageId).ToList();
                var missingMessageIds = messageIds.Except(existingMessageIds).ToList();

                foreach (var messageId in missingMessageIds) {
                    // Verify message exists and belongs to the conversation
                    var messageExists = await _context.Messages
                        .AnyAsync(m => m.Id == messageId && m.ConversationId == conversationId && !m.IsDeleted);

                    if (messageExists) {
                        _context.MessageStatuses.Add(new MessageStatus {
                            MessageId = messageId,
                            UserId = userId,
                            Status = DeliveryStatus.Read
                        });
                    }
                }

                // Update participant's last read message
                if (messageIds.Any()) {
                    var lastMessage = await _context.Messages
                        .Where(m => messageIds.Contains(m.Id) && m.ConversationId == conversationId)
                        .OrderByDescending(m => m.SentAt)
                        .FirstOrDefaultAsync();

                    if (lastMessage != null) {
                        participant.LastReadMessageId = lastMessage.Id;
                        participant.LastReadAt = DateTime.UtcNow;
                    }
                }

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Messages marked as read successfully",
                    Data = new { markedCount = messageIds.Count }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error marking messages as read for user {userId} in conversation {conversationId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error marking messages as read"
                };
            }
        }

        public async Task<Response> GetUnreadMessagesCountAsync(string userId) {
            try {
                var unreadCounts = await _context.ConversationParticipants
                    .Where(cp => cp.UserId == userId && cp.IsActive)
                    .Select(cp => new {
                        conversationId = cp.ConversationId,
                        conversationName = cp.Conversation.Name,
                        unreadCount = _context.MessageStatuses
                            .Count(ms => ms.UserId == userId &&
                                       ms.Message.ConversationId == cp.ConversationId &&
                                       ms.Status != DeliveryStatus.Read)
                    })
                    .Where(x => x.unreadCount > 0)
                    .ToListAsync();

                var totalUnreadCount = unreadCounts.Sum(x => x.unreadCount);

                return new Response {
                    IsSuccess = true,
                    Message = "Unread messages count retrieved successfully",
                    Data = new {
                        totalUnreadCount = totalUnreadCount,
                        conversationCounts = unreadCounts
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error getting unread messages count for user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving unread messages count"
                };
            }
        }

        public async Task<Response> UpdateTypingStatusAsync(string userId, string conversationId, bool isTyping) {
            try {
                var existingIndicator = await _context.TypingIndicators
                    .FirstOrDefaultAsync(ti => ti.UserId == userId && ti.ConversationId == conversationId);

                if (isTyping) {
                    if (existingIndicator == null) {
                        _context.TypingIndicators.Add(new TypingIndicator {
                            UserId = userId,
                            ConversationId = conversationId,
                            IsActive = true
                        });
                    }
                    else {
                        existingIndicator.IsActive = true;
                        existingIndicator.LastUpdatedAt = DateTime.UtcNow;
                    }
                }
                else {
                    if (existingIndicator != null) {
                        existingIndicator.IsActive = false;
                        existingIndicator.LastUpdatedAt = DateTime.UtcNow;
                    }
                }

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Typing status updated successfully"
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error updating typing status for user {userId} in conversation {conversationId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error updating typing status"
                };
            }
        }

        public async Task<Response> UpdateUserPresenceAsync(string userId, string status) {
            try {
                var presence = new UserPresence {
                    UserId = userId,
                    Status = Enum.Parse<PresenceStatus>(status, true)
                };

                _context.UserPresences.Add(presence);

                // Update user's online status
                var user = await _context.Users.FindAsync(userId);
                if (user != null) {
                    user.IsOnline = status.ToLower() == "online";
                    user.LastSeen = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "User presence updated successfully"
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Error updating presence for user {userId}");
                return new Response {
                    IsSuccess = false,
                    Message = "Error updating user presence"
                };
            }
        }

        public async Task<Response> GetUserPresenceAsync(string userId) {
            try {
                var user = await _context.Users.FindAsync(userId);
                if (user == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "User not found"
                    };
                }

                // Get latest presence record
                var latestPresence = await _context.UserPresences
                    .Where(up => up.UserId == userId)
                    .OrderByDescending(up => up.StatusAt)
                    .FirstOrDefaultAsync();

                var presenceData = new {
                    userId = userId,
                    userName = user.FullName,
                    isOnline = user.IsOnline,
                    lastSeen = user.LastSeen,
                    currentStatus = latestPresence?.Status.ToString().ToLower() ?? "offline",
                    statusUpdatedAt = latestPresence?.StatusAt
                };

                return new Response {
                    IsSuccess = true,
                    Message = "User presence retrieved successfully",
                    Data = presenceData
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error getting user presence for user {UserId}", userId);
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving user presence"
                };
            }
        }

        public async Task<Response> GetOnlineUsersAsync() {
            try {
                var onlineUsers = await _context.Users
                    .Where(u => u.IsOnline && u.IsActive)
                    .Select(u => new {
                        userId = u.Id,
                        userName = u.FullName,
                        email = u.Email,
                        profilePictureUrl = u.ProfilePictureUrl,
                        lastSeen = u.LastSeen
                    })
                    .ToListAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Online users retrieved successfully",
                    Data = new {
                        onlineCount = onlineUsers.Count,
                        users = onlineUsers
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error getting online users");
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving online users"
                };
            }
        }

        public async Task<Response> SearchMessagesAsync(string userId, string query, string? conversationId = null, int page = 1, int pageSize = 20) {
            try {
                if (string.IsNullOrWhiteSpace(query)) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Search query cannot be empty"
                    };
                }

                // Get user's accessible conversations
                var userConversationIds = await _context.ConversationParticipants
                    .Where(cp => cp.UserId == userId && cp.IsActive)
                    .Select(cp => cp.ConversationId)
                    .ToListAsync();

                IQueryable<Message> messagesQuery = _context.Messages
                    .Where(m => userConversationIds.Contains(m.ConversationId) &&
                               !m.IsDeleted &&
                               m.Content.Contains(query))
                    .Include(m => m.Sender)
                    .Include(m => m.Conversation);

                // Filter by specific conversation if provided
                if (!string.IsNullOrEmpty(conversationId)) {
                    messagesQuery = messagesQuery.Where(m => m.ConversationId == conversationId);
                }

                var messages = await messagesQuery
                    .OrderByDescending(m => m.SentAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(m => new {
                        messageId = m.Id,
                        content = m.Content,
                        type = m.Type.ToString().ToLower(),
                        senderId = m.SenderId,
                        senderName = m.Sender.FullName,
                        sentAt = m.SentAt,
                        conversationId = m.ConversationId,
                        conversationName = m.Conversation.Name,
                        fileUrl = m.FileUrl,
                        fileName = m.FileName
                    })
                    .ToListAsync();

                var totalCount = await messagesQuery.CountAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Messages search completed successfully",
                    Data = new {
                        messages = messages,
                        totalCount = totalCount,
                        page = page,
                        pageSize = pageSize,
                        totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error searching messages for user {UserId} with query {Query}", userId, query);
                return new Response {
                    IsSuccess = false,
                    Message = "Error searching messages"
                };
            }
        }

        public async Task<Response> SearchConversationsAsync(string userId, string query, int page = 1, int pageSize = 20) {
            try {
                if (string.IsNullOrWhiteSpace(query)) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Search query cannot be empty"
                    };
                }

                var conversations = await _context.ConversationParticipants
                    .Where(cp => cp.UserId == userId && cp.IsActive &&
                               (cp.Conversation.Name.Contains(query) ||
                                cp.Conversation.Description.Contains(query)))
                    .Include(cp => cp.Conversation)
                    .ThenInclude(c => c.Participants)
                    .ThenInclude(p => p.User)
                    .Include(cp => cp.Conversation)
                    .ThenInclude(c => c.LastMessage)
                    .OrderByDescending(cp => cp.Conversation.LastMessageAt ?? cp.Conversation.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(cp => new {
                        conversationId = cp.Conversation.Id,
                        name = cp.Conversation.Name,
                        description = cp.Conversation.Description,
                        type = cp.Conversation.Type.ToString().ToLower(),
                        participantCount = cp.Conversation.Participants.Count(p => p.IsActive),
                        lastMessage = cp.Conversation.LastMessage != null ? new {
                            content = cp.Conversation.LastMessage.Content,
                            sentAt = cp.Conversation.LastMessage.SentAt
                        } : null,
                        lastActivity = cp.Conversation.LastMessageAt ?? cp.Conversation.CreatedAt
                    })
                    .ToListAsync();

                var totalCount = await _context.ConversationParticipants
                    .Where(cp => cp.UserId == userId && cp.IsActive &&
                               (cp.Conversation.Name.Contains(query) ||
                                cp.Conversation.Description.Contains(query)))
                    .CountAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Conversations search completed successfully",
                    Data = new {
                        conversations = conversations,
                        totalCount = totalCount,
                        page = page,
                        pageSize = pageSize,
                        totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error searching conversations for user {UserId} with query {Query}", userId, query);
                return new Response {
                    IsSuccess = false,
                    Message = "Error searching conversations"
                };
            }
        }

        public async Task<Response> UploadFileAsync(string userId, string conversationId, Stream fileStream, string fileName, string contentType) {
            try {
                // Verify user is participant in conversation
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                // Validate file
                if (fileStream == null || fileStream.Length == 0) {
                    return new Response {
                        IsSuccess = false,
                        Message = "File is empty or invalid"
                    };
                }

                // Check file size (limit to 10MB)
                const long maxFileSize = 10 * 1024 * 1024;
                if (fileStream.Length > maxFileSize) {
                    return new Response {
                        IsSuccess = false,
                        Message = "File size exceeds 10MB limit"
                    };
                }

                // Create IFormFile from Stream for S3 upload
                var formFile = new StreamFormFile(fileStream, fileName, contentType);
                var folderName = $"chat-files/{conversationId}";
                var fileKey = await _s3Service.UploadFileAsync(formFile, folderName);

                if (string.IsNullOrEmpty(fileKey)) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Failed to upload file"
                    };
                }

                // Get the full URL for the uploaded file
                var fileUrl = _s3Service.GetFileUrl(fileKey);

                // Determine message type based on content type
                var messageType = GetMessageTypeFromContentType(contentType);

                // Create message with file
                var message = new Message {
                    ConversationId = conversationId,
                    SenderId = userId,
                    Content = $"Shared a file: {fileName}",
                    Type = messageType,
                    FileUrl = fileUrl,
                    FileName = fileName,
                    FileSize = fileStream.Length,
                    MimeType = contentType
                };

                _context.Messages.Add(message);

                // Update conversation last message
                var conversation = await _context.Conversations.FindAsync(conversationId);
                if (conversation != null) {
                    conversation.LastMessageId = message.Id;
                    conversation.LastMessageAt = message.SentAt;
                    conversation.UpdatedAt = DateTime.UtcNow;
                }

                // Create message status for all participants except sender
                var participants = await _context.ConversationParticipants
                    .Where(cp => cp.ConversationId == conversationId && cp.IsActive && cp.UserId != userId)
                    .ToListAsync();

                foreach (var p in participants) {
                    _context.MessageStatuses.Add(new MessageStatus {
                        MessageId = message.Id,
                        UserId = p.UserId,
                        Status = DeliveryStatus.Sent
                    });
                }

                await _context.SaveChangesAsync();

                // Get sender details for response
                var sender = await _context.Users.FindAsync(userId);
                var messageResponse = new {
                    messageId = message.Id,
                    conversationId = message.ConversationId,
                    senderId = message.SenderId,
                    senderName = sender?.FullName ?? "Unknown",
                    content = message.Content,
                    type = message.Type.ToString().ToLower(),
                    sentAt = message.SentAt,
                    fileUrl = message.FileUrl,
                    fileName = message.FileName,
                    fileSize = message.FileSize,
                    mimeType = message.MimeType
                };

                return new Response {
                    IsSuccess = true,
                    Message = "File uploaded and message sent successfully",
                    Data = messageResponse
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error uploading file for user {UserId} in conversation {ConversationId}", userId, conversationId);
                return new Response {
                    IsSuccess = false,
                    Message = "Error uploading file"
                };
            }
        }

        // Missing interface methods implementation
        public async Task<Response> GetMessageAsync(string messageId, string userId) {
            try {
                var message = await _context.Messages
                    .Include(m => m.Sender)
                    .Include(m => m.Conversation)
                    .ThenInclude(c => c.Participants)
                    .Include(m => m.ReplyToMessage)
                    .ThenInclude(rm => rm.Sender)
                    .FirstOrDefaultAsync(m => m.Id == messageId && !m.IsDeleted);

                if (message == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Message not found"
                    };
                }

                // Verify user has access to this message
                var hasAccess = message.Conversation.Participants
                    .Any(p => p.UserId == userId && p.IsActive);

                if (!hasAccess) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You don't have access to this message"
                    };
                }

                var messageData = new {
                    messageId = message.Id,
                    content = message.Content,
                    type = message.Type.ToString().ToLower(),
                    senderId = message.SenderId,
                    senderName = message.Sender.FullName,
                    sentAt = message.SentAt,
                    editedAt = message.EditedAt,
                    conversationId = message.ConversationId,
                    replyTo = message.ReplyToMessage != null ? new {
                        messageId = message.ReplyToMessage.Id,
                        content = message.ReplyToMessage.Content,
                        senderName = message.ReplyToMessage.Sender.FullName
                    } : null,
                    fileUrl = message.FileUrl,
                    fileName = message.FileName,
                    fileSize = message.FileSize,
                    mimeType = message.MimeType
                };

                return new Response {
                    IsSuccess = true,
                    Message = "Message retrieved successfully",
                    Data = messageData
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error getting message {MessageId} for user {UserId}", messageId, userId);
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving message"
                };
            }
        }

        public async Task<Response> UpdateConversationAsync(string conversationId, string userId, string? name = null, string? description = null) {
            try {
                // Verify user has permission to update conversation
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive &&
                                             (cp.Role == ParticipantRole.Admin || cp.Role == ParticipantRole.Owner));

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You don't have permission to update this conversation"
                    };
                }

                var conversation = await _context.Conversations.FindAsync(conversationId);
                if (conversation == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Conversation not found"
                    };
                }

                // Update conversation details
                if (!string.IsNullOrWhiteSpace(name)) {
                    conversation.Name = name;
                }

                if (description != null) {
                    conversation.Description = description;
                }

                conversation.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Conversation updated successfully",
                    Data = new {
                        conversationId = conversation.Id,
                        name = conversation.Name,
                        description = conversation.Description,
                        updatedAt = conversation.UpdatedAt
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error updating conversation {ConversationId} by user {UserId}", conversationId, userId);
                return new Response {
                    IsSuccess = false,
                    Message = "Error updating conversation"
                };
            }
        }

        public async Task<Response> DeleteConversationAsync(string conversationId, string userId) {
            try {
                // Verify user has permission to delete conversation (only owner can delete)
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId &&
                                             cp.IsActive && cp.Role == ParticipantRole.Owner);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You don't have permission to delete this conversation"
                    };
                }

                var conversation = await _context.Conversations.FindAsync(conversationId);
                if (conversation == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Conversation not found"
                    };
                }

                // Soft delete the conversation
                conversation.IsActive = false;
                conversation.UpdatedAt = DateTime.UtcNow;

                // Deactivate all participants
                var participants = await _context.ConversationParticipants
                    .Where(cp => cp.ConversationId == conversationId && cp.IsActive)
                    .ToListAsync();

                foreach (var p in participants) {
                    p.IsActive = false;
                    p.LeftAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Conversation deleted successfully"
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error deleting conversation {ConversationId} by user {UserId}", conversationId, userId);
                return new Response {
                    IsSuccess = false,
                    Message = "Error deleting conversation"
                };
            }
        }

        public async Task<Response> RemoveParticipantAsync(string conversationId, string adminId, string participantId) {
            try {
                // Verify admin has permission
                var adminParticipant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == adminId &&
                                             cp.IsActive && (cp.Role == ParticipantRole.Admin || cp.Role == ParticipantRole.Owner));

                if (adminParticipant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You don't have permission to remove participants"
                    };
                }

                // Find participant to remove
                var participantToRemove = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == participantId && cp.IsActive);

                if (participantToRemove == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Participant not found in this conversation"
                    };
                }

                // Prevent removing the owner unless admin is also owner
                if (participantToRemove.Role == ParticipantRole.Owner && adminParticipant.Role != ParticipantRole.Owner) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Cannot remove conversation owner"
                    };
                }

                // Remove participant
                participantToRemove.IsActive = false;
                participantToRemove.LeftAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Participant removed successfully"
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error removing participant {ParticipantId} from conversation {ConversationId}", participantId, conversationId);
                return new Response {
                    IsSuccess = false,
                    Message = "Error removing participant"
                };
            }
        }

        public async Task<Response> UpdateParticipantRoleAsync(string conversationId, string adminId, string participantId, ParticipantRole role) {
            try {
                // Verify admin has permission (only owner can change roles)
                var adminParticipant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == adminId &&
                                             cp.IsActive && cp.Role == ParticipantRole.Owner);

                if (adminParticipant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You don't have permission to change participant roles"
                    };
                }

                // Find participant to update
                var participant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == participantId && cp.IsActive);

                if (participant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Participant not found in this conversation"
                    };
                }

                // Prevent changing owner role to something else
                if (participant.Role == ParticipantRole.Owner && role != ParticipantRole.Owner) {
                    return new Response {
                        IsSuccess = false,
                        Message = "Cannot change owner role. Transfer ownership first."
                    };
                }

                participant.Role = role;
                await _context.SaveChangesAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Participant role updated successfully",
                    Data = new {
                        participantId = participantId,
                        newRole = role.ToString().ToLower()
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error updating participant role for {ParticipantId} in conversation {ConversationId}", participantId, conversationId);
                return new Response {
                    IsSuccess = false,
                    Message = "Error updating participant role"
                };
            }
        }

        public async Task<Response> GetConversationParticipantsAsync(string conversationId, string userId) {
            try {
                // Verify user is participant in conversation
                var userParticipant = await _context.ConversationParticipants
                    .FirstOrDefaultAsync(cp => cp.ConversationId == conversationId && cp.UserId == userId && cp.IsActive);

                if (userParticipant == null) {
                    return new Response {
                        IsSuccess = false,
                        Message = "You are not a participant in this conversation"
                    };
                }

                var participants = await _context.ConversationParticipants
                    .Where(cp => cp.ConversationId == conversationId && cp.IsActive)
                    .Include(cp => cp.User)
                    .Select(cp => new {
                        userId = cp.UserId,
                        userName = cp.User.FullName,
                        email = cp.User.Email,
                        profilePictureUrl = cp.User.ProfilePictureUrl,
                        role = cp.Role.ToString().ToLower(),
                        joinedAt = cp.JoinedAt,
                        isOnline = cp.User.IsOnline,
                        lastSeen = cp.User.LastSeen
                    })
                    .ToListAsync();

                return new Response {
                    IsSuccess = true,
                    Message = "Conversation participants retrieved successfully",
                    Data = new {
                        conversationId = conversationId,
                        participantCount = participants.Count,
                        participants = participants
                    }
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error getting participants for conversation {ConversationId}", conversationId);
                return new Response {
                    IsSuccess = false,
                    Message = "Error retrieving conversation participants"
                };
            }
        }

        private static MessageType GetMessageTypeFromContentType(string contentType) {
            return contentType.ToLower() switch {
                var ct when ct.StartsWith("image/") => MessageType.Image,
                var ct when ct.StartsWith("video/") => MessageType.Video,
                var ct when ct.StartsWith("audio/") => MessageType.Audio,
                _ => MessageType.File
            };
        }
    }

    // Helper class to convert Stream to IFormFile
    public class StreamFormFile : IFormFile {
        private readonly Stream _stream;
        private readonly string _fileName;
        private readonly string _contentType;

        public StreamFormFile(Stream stream, string fileName, string contentType) {
            _stream = stream;
            _fileName = fileName;
            _contentType = contentType;
        }

        public string ContentType => _contentType;
        public string ContentDisposition => $"form-data; name=\"file\"; filename=\"{_fileName}\"";
        public IHeaderDictionary Headers => new HeaderDictionary();
        public long Length => _stream.Length;
        public string Name => "file";
        public string FileName => _fileName;

        public void CopyTo(Stream target) {
            _stream.CopyTo(target);
        }

        public async Task CopyToAsync(Stream target, CancellationToken cancellationToken = default) {
            await _stream.CopyToAsync(target, cancellationToken);
        }

        public Stream OpenReadStream() {
            return _stream;
        }
    }
}

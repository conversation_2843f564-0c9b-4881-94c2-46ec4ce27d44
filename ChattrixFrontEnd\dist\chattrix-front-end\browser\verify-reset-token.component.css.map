{"version": 3, "sources": ["src/app/Pages/authentication/Pages/verify-reset-token/verify-reset-token.component.scss"], "sourcesContent": ["/* Verify Reset Token Component Specific Styles */\r\n\r\n.token-card {\r\n  max-width: 420px;\r\n}\r\n\r\n.full-width {\r\n  width: 100%;\r\n}\r\n\r\n/* Token Input Container */\r\n.token-container {\r\n  margin-bottom: var(--spacing-xl);\r\n}\r\n\r\n.token-inputs {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: var(--spacing-sm);\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n.token-input {\r\n  width: 50px;\r\n  height: 60px;\r\n  border: 2px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background-color: var(--bg-input);\r\n  color: var(--text-primary);\r\n  font-size: var(--font-size-xl);\r\n  font-weight: 600;\r\n  text-align: center;\r\n  outline: none;\r\n  transition: all var(--transition-normal);\r\n\r\n  &:focus {\r\n    border-color: var(--accent-green);\r\n    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);\r\n    background-color: var(--bg-secondary);\r\n  }\r\n\r\n  &:hover {\r\n    border-color: var(--border-secondary);\r\n  }\r\n\r\n  &.error {\r\n    border-color: var(--error);\r\n    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* Token Error Message */\r\n.token-error {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-xs);\r\n  color: var(--error);\r\n  font-size: var(--font-size-sm);\r\n  margin-top: var(--spacing-sm);\r\n\r\n  .error-icon {\r\n    font-size: 16px;\r\n    width: 16px;\r\n    height: 16px;\r\n  }\r\n}\r\n\r\n/* Verify Button */\r\n.verify-button {\r\n  height: 44px;\r\n  font-size: var(--font-size-base);\r\n  font-weight: 500;\r\n  text-transform: none;\r\n  border-radius: var(--radius-md);\r\n  margin-bottom: var(--spacing-xl);\r\n  position: relative;\r\n\r\n  &:disabled {\r\n    opacity: 0.6;\r\n  }\r\n\r\n  .button-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: var(--spacing-sm);\r\n    position: relative;\r\n  }\r\n\r\n  .hidden {\r\n    visibility: hidden;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.button-spinner {\r\n  position: absolute;\r\n\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* Resend Section */\r\n.resend-section {\r\n  text-align: center;\r\n  margin-bottom: var(--spacing-lg);\r\n}\r\n\r\n.resend-text {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n.resend-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-xs);\r\n  font-size: var(--font-size-sm);\r\n  text-transform: none;\r\n  margin: 0 auto;\r\n  position: relative;\r\n\r\n  &:not(.disabled) {\r\n    color: var(--accent-green) !important;\r\n\r\n    &:hover {\r\n      color: var(--accent-green-hover) !important;\r\n      background: transparent;\r\n    }\r\n  }\r\n\r\n  &.disabled {\r\n    color: var(--text-disabled) !important;\r\n    cursor: not-allowed;\r\n  }\r\n\r\n  .button-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: var(--spacing-xs);\r\n    position: relative;\r\n  }\r\n\r\n  .hidden {\r\n    visibility: hidden;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.resend-spinner {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* Back to Login */\r\n.back-to-login {\r\n  text-align: center;\r\n  padding-top: var(--spacing-lg);\r\n}\r\n\r\n.back-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-xs);\r\n  color: var(--accent-green) !important;\r\n  text-transform: none;\r\n  font-size: var(--font-size-sm);\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    color: var(--accent-green-hover) !important;\r\n    background: transparent;\r\n  }\r\n}\r\n\r\n/* Logo Fallback Styles */\r\n.logo-fallback {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--accent-green) 0%,\r\n    var(--accent-green-light) 100%\r\n  );\r\n  border-radius: 50%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* Snackbar Styles */\r\n::ng-deep .error-snackbar {\r\n  background-color: var(--error) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n::ng-deep .success-snackbar {\r\n  background-color: var(--success) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 480px) {\r\n  .token-card {\r\n    margin: var(--spacing-sm);\r\n    max-width: calc(100vw - 2rem);\r\n  }\r\n\r\n  .auth-header {\r\n    padding: var(--spacing-lg) var(--spacing-md);\r\n  }\r\n\r\n  .auth-form {\r\n    padding: var(--spacing-lg) var(--spacing-md);\r\n  }\r\n\r\n  .auth-footer {\r\n    padding: var(--spacing-md);\r\n  }\r\n\r\n  .auth-logo {\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .auth-title {\r\n    font-size: var(--font-size-lg);\r\n  }\r\n\r\n  .auth-subtitle {\r\n    font-size: var(--font-size-xs);\r\n  }\r\n\r\n  .token-input {\r\n    width: 45px;\r\n    height: 55px;\r\n    font-size: var(--font-size-lg);\r\n  }\r\n\r\n  .token-inputs {\r\n    gap: var(--spacing-xs);\r\n  }\r\n}\r\n\r\n@media (max-width: 360px) {\r\n  .token-input {\r\n    width: 40px;\r\n    height: 50px;\r\n    font-size: var(--font-size-base);\r\n  }\r\n}\r\n\r\n/* Focus and Hover States */\r\n.mat-mdc-button:focus {\r\n  outline: 2px solid var(--accent-green);\r\n  outline-offset: 2px;\r\n}\r\n\r\n/* Loading State Animation */\r\n.button-spinner,\r\n.resend-spinner {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Token Input Animation */\r\n.token-input {\r\n  animation: fadeIn 0.3s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Success State Animation */\r\n.token-input.success {\r\n  animation: successPulse 0.6s ease-in-out;\r\n  border-color: var(--success);\r\n  background-color: rgba(16, 185, 129, 0.1);\r\n}\r\n\r\n@keyframes successPulse {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.05);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n/* Timer Display Styling */\r\n.resend-button .mat-icon {\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.5;\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n  }\r\n}\r\n"], "mappings": ";AAEA,CAAA;AACE,aAAA;;AAGF,CAAA;AACE,SAAA;;AAIF,CAAA;AACE,iBAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA,IAAA;AACA,oBAAA,IAAA;AACA,SAAA,IAAA;AACA,aAAA,IAAA;AACA,eAAA;AACA,cAAA;AACA,WAAA;AACA,cAAA,IAAA,IAAA;;AAEA,CAbF,WAaE;AACE,gBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,oBAAA,IAAA;;AAGF,CAnBF,WAmBE;AACE,gBAAA,IAAA;;AAGF,CAvBF,WAuBE,CAAA;AACE,gBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;;AAGF,CA5BF,WA4BE;AACE,WAAA;AACA,UAAA;;AAKJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA,IAAA;AACA,SAAA,IAAA;AACA,aAAA,IAAA;AACA,cAAA,IAAA;;AAEA,CARF,YAQE,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAKJ,CAAA;AACE,UAAA;AACA,aAAA,IAAA;AACA,eAAA;AACA,kBAAA;AACA,iBAAA,IAAA;AACA,iBAAA,IAAA;AACA,YAAA;;AAEA,CATF,aASE;AACE,WAAA;;AAGF,CAbF,cAaE,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,YAAA;;AAGF,CArBF,cAqBE,CAAA;AACE,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,YAAA;AAEA,aAAA,UAAA,IAAA,EAAA;;AAIF,CAAA;AACE,cAAA;AACA,iBAAA,IAAA;;AAGF,CAAA;AACE,SAAA,IAAA;AACA,aAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA,IAAA;AACA,aAAA,IAAA;AACA,kBAAA;AACA,UAAA,EAAA;AACA,YAAA;;AAEA,CATF,aASE,KAAA,CAAA;AACE,SAAA,IAAA;;AAEA,CAZJ,aAYI,KAAA,CAHF,SAGE;AACE,SAAA,IAAA;AACA,cAAA;;AAIJ,CAlBF,aAkBE,CATA;AAUE,SAAA,IAAA;AACA,UAAA;;AAGF,CAvBF,cAuBE,CAvDA;AAwDE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,YAAA;;AAGF,CA/BF,cA+BE,CAvDA;AAwDE,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,YAAA;AACA,QAAA;AACA,OAAA;AACA,aAAA,UAAA,IAAA,EAAA;;AAIF,CAAA;AACE,cAAA;AACA,eAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA,IAAA;AACA,SAAA,IAAA;AACA,kBAAA;AACA,aAAA,IAAA;AACA,UAAA,EAAA;;AAEA,CATF,WASE;AACE,SAAA,IAAA;AACA,cAAA;;AAKJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA;IAAA;MAAA,MAAA;MAAA,IAAA,gBAAA,EAAA;MAAA,IAAA,sBAAA;AAKA,iBAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,eAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAIF,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAGF,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAIF,OAAA,CAAA,SAAA,EAAA;AACE,GAxNF;AAyNI,YAAA,IAAA;AACA,eAAA,KAAA,MAAA,EAAA;;AAGF,GAAA;AACE,aAAA,IAAA,cAAA,IAAA;;AAGF,GAAA;AACE,aAAA,IAAA,cAAA,IAAA;;AAGF,GAAA;AACE,aAAA,IAAA;;AAGF,GAAA;AACE,WAAA;AACA,YAAA;;AAGF,GAAA;AACE,eAAA,IAAA;;AAGF,GAAA;AACE,eAAA,IAAA;;AAGF,GAlOF;AAmOI,WAAA;AACA,YAAA;AACA,eAAA,IAAA;;AAGF,GA/OF;AAgPI,SAAA,IAAA;;;AAIJ,OAAA,CAAA,SAAA,EAAA;AACE,GA9OF;AA+OI,WAAA;AACA,YAAA;AACA,eAAA,IAAA;;;AAKJ,CAAA,cAAA;AACE,WAAA,IAAA,MAAA,IAAA;AACA,kBAAA;;AAIF,CA9KA;AA8KA,CAvHA;AAyHE,aAAA,KAAA,GAAA,OAAA;;AAGF,WAHE;AAIA;AACE,eAAA,OAAA;;AAEF;AACE,eAAA,OAAA;;;AAKJ,CA3QA;AA4QE,aAAA,OAAA,KAAA;;AAGF,WAHE;AAIA;AACE,aAAA;AACA,eAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA;;;AAKJ,CA3RA,WA2RA,CAAA;AACE,aAAA,aAAA,KAAA;AACA,gBAAA,IAAA;AACA,oBAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,WALE;AAMA;AACE,eAAA,MAAA;;AAEF;AACE,eAAA,MAAA;;AAEF;AACE,eAAA,MAAA;;;AAKJ,CA9MA,cA8MA,CAAA;AACE,aAAA,MAAA,GAAA;;AAGF,WAHE;AAIA;AACE,aAAA;;AAEF;AACE,aAAA;;AAEF;AACE,aAAA;;;", "names": []}
{"version": 3, "sources": ["src/app/Pages/authentication/Pages/profile-picture/profile-picture.component.scss"], "sourcesContent": ["/* Profile Picture Component Styles */\r\n\r\n.profile-card {\r\n  max-width: 520px; // Slightly wider for profile completion\r\n\r\n  // Specific optimizations for profile form\r\n  .auth-header {\r\n    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-xs);\r\n  }\r\n\r\n  .auth-form {\r\n    padding: var(--spacing-sm) var(--spacing-lg);\r\n  }\r\n\r\n  .auth-footer {\r\n    padding: var(--spacing-sm) var(--spacing-lg);\r\n  }\r\n\r\n  .auth-logo {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-bottom: var(--spacing-sm);\r\n  }\r\n\r\n  .auth-title {\r\n    font-size: var(--font-size-lg);\r\n    margin-bottom: var(--spacing-xs);\r\n  }\r\n\r\n  .auth-subtitle {\r\n    font-size: var(--font-size-xs);\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n/* Profile Picture Upload Section */\r\n.profile-picture-section {\r\n  margin-bottom: var(--spacing-lg);\r\n  text-align: center;\r\n}\r\n\r\n.profile-picture-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: var(--spacing-md);\r\n}\r\n\r\n.profile-picture-preview {\r\n  position: relative;\r\n  width: 120px;\r\n  height: 120px;\r\n  border-radius: 50%;\r\n  border: 3px dashed var(--border-color);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  background-color: var(--bg-secondary);\r\n\r\n  &.has-image {\r\n    border: 3px solid var(--accent-green);\r\n    border-style: solid;\r\n  }\r\n\r\n  &:hover {\r\n    border-color: var(--accent-green);\r\n    background-color: var(--bg-tertiary);\r\n  }\r\n}\r\n\r\n.preview-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  border-radius: 50%;\r\n}\r\n\r\n.placeholder-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: var(--spacing-xs);\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 2.5rem;\r\n  width: 2.5rem;\r\n  height: 2.5rem;\r\n  color: var(--text-disabled);\r\n}\r\n\r\n.upload-text {\r\n  font-size: var(--font-size-sm);\r\n  font-weight: 500;\r\n}\r\n\r\n.remove-image-btn {\r\n  position: absolute;\r\n  top: -8px;\r\n  right: -8px;\r\n  background-color: var(--error);\r\n  color: white;\r\n  width: 28px;\r\n  height: 28px;\r\n  min-width: 28px;\r\n\r\n  &:hover {\r\n    background-color: var(--error-dark);\r\n  }\r\n\r\n  .mat-icon {\r\n    font-size: 16px;\r\n    width: 16px;\r\n    height: 16px;\r\n  }\r\n}\r\n\r\n.upload-button {\r\n  min-width: 140px;\r\n  height: 40px;\r\n  border-radius: var(--radius-md);\r\n  font-weight: 500;\r\n  text-transform: none;\r\n\r\n  .mat-icon {\r\n    margin-right: var(--spacing-xs);\r\n  }\r\n}\r\n\r\n.upload-hint {\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n  margin: 0;\r\n  text-align: center;\r\n}\r\n\r\n/* Form Styling */\r\n.full-width {\r\n  width: 100%;\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n/* Button Group */\r\n.button-group {\r\n  display: flex;\r\n  gap: var(--spacing-md);\r\n  margin-top: var(--spacing-lg);\r\n  justify-content: space-between;\r\n}\r\n\r\n.back-button {\r\n  flex: 0 0 auto;\r\n  min-width: 100px;\r\n  height: 44px;\r\n  border-radius: var(--radius-md);\r\n  font-weight: 500;\r\n  text-transform: none;\r\n\r\n  .mat-icon {\r\n    margin-right: var(--spacing-xs);\r\n  }\r\n}\r\n\r\n.complete-button {\r\n  flex: 1;\r\n  height: 44px;\r\n  font-size: var(--font-size-base);\r\n  font-weight: 500;\r\n  text-transform: none;\r\n  border-radius: var(--radius-md);\r\n  position: relative;\r\n\r\n  &:disabled {\r\n    opacity: 0.6;\r\n  }\r\n\r\n  .button-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: var(--spacing-sm);\r\n    position: relative;\r\n  }\r\n\r\n  .hidden {\r\n    visibility: hidden;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.button-spinner {\r\n  position: absolute;\r\n\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* Footer */\r\n.footer-text {\r\n  margin: var(--spacing-sm) 0 0;\r\n  text-align: center;\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-xs);\r\n  line-height: 1.4;\r\n}\r\n"], "mappings": ";AAEA,CAAA;AACE,aAAA;;AAGA,CAJF,aAIE,CAAA;AACE,WAAA,IAAA,cAAA,IAAA,cAAA,IAAA;;AAGF,CARF,aAQE,CAAA;AACE,WAAA,IAAA,cAAA,IAAA;;AAGF,CAZF,aAYE,CAAA;AACE,WAAA,IAAA,cAAA,IAAA;;AAGF,CAhBF,aAgBE,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA,IAAA;;AAGF,CAtBF,aAsBE,CAAA;AACE,aAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,CA3BF,aA2BE,CAAA;AACE,aAAA,IAAA;AACA,iBAAA;;AAKJ,CAAA;AACE,iBAAA,IAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA,IAAA;;AAGF,CAAA;AACE,YAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,UAAA,IAAA,OAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,YAAA;AACA,cAAA,IAAA,KAAA;AACA,oBAAA,IAAA;;AAEA,CAbF,uBAaE,CAAA;AACE,UAAA,IAAA,MAAA,IAAA;AACA,gBAAA;;AAGF,CAlBF,uBAkBE;AACE,gBAAA,IAAA;AACA,oBAAA,IAAA;;AAIJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,cAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA,IAAA;AACA,SAAA,IAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA,IAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,eAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,oBAAA,IAAA;AACA,SAAA;AACA,SAAA;AACA,UAAA;AACA,aAAA;;AAEA,CAVF,gBAUE;AACE,oBAAA,IAAA;;AAGF,CAdF,iBAcE,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CAAA;AACE,aAAA;AACA,UAAA;AACA,iBAAA,IAAA;AACA,eAAA;AACA,kBAAA;;AAEA,CAPF,cAOE,CAdA;AAeE,gBAAA,IAAA;;AAIJ,CAAA;AACE,aAAA,IAAA;AACA,SAAA,IAAA;AACA,UAAA;AACA,cAAA;;AAIF,CAAA;AACE,SAAA;AACA,iBAAA,IAAA;;AAIF,CAAA;AACE,WAAA;AACA,OAAA,IAAA;AACA,cAAA,IAAA;AACA,mBAAA;;AAGF,CAAA;AACE,QAAA,EAAA,EAAA;AACA,aAAA;AACA,UAAA;AACA,iBAAA,IAAA;AACA,eAAA;AACA,kBAAA;;AAEA,CARF,YAQE,CAhDA;AAiDE,gBAAA,IAAA;;AAIJ,CAAA;AACE,QAAA;AACA,UAAA;AACA,aAAA,IAAA;AACA,eAAA;AACA,kBAAA;AACA,iBAAA,IAAA;AACA,YAAA;;AAEA,CATF,eASE;AACE,WAAA;;AAGF,CAbF,gBAaE,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,YAAA;;AAGF,CArBF,gBAqBE,CAAA;AACE,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,YAAA;AAEA,aAAA,UAAA,IAAA,EAAA;;AAIF,CAAA;AACE,UAAA,IAAA,cAAA,EAAA;AACA,cAAA;AACA,SAAA,IAAA;AACA,aAAA,IAAA;AACA,eAAA;;", "names": []}
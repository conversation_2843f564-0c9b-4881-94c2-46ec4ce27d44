﻿using ChattrixBackend.Core.Entities.UserManagement.PasswordResetTokenModel;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;
using ChattrixBackend.Core.Entities.UserManagement.UserOtpModel;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace ChattrixBackend.EntityFramworkCore.Data {
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser> {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
           : base(options) {
        }

        public DbSet<UserOtp> UserOtps { get; set; }
        public DbSet<PasswordResetToken> PasswordResetTokens { get; set; }
    }
}

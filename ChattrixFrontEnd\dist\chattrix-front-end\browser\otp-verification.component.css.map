{"version": 3, "sources": ["src/app/Pages/authentication/Pages/otp-verification/otp-verification.component.scss"], "sourcesContent": ["/* OTP Verification Component Specific Styles */\r\n\r\n.otp-card {\r\n  max-width: 420px;\r\n}\r\n\r\n.full-width {\r\n  width: 100%;\r\n}\r\n\r\n/* OTP Input Container */\r\n.otp-container {\r\n  margin-bottom: var(--spacing-xl);\r\n}\r\n\r\n.otp-input-field {\r\n  width: 100%;\r\n\r\n  .mat-mdc-text-field-wrapper {\r\n    background-color: var(--bg-input);\r\n    border-radius: var(--radius-md);\r\n  }\r\n\r\n  .mat-mdc-form-field-input {\r\n    color: var(--text-primary);\r\n    font-size: var(--font-size-xl);\r\n    font-weight: 600;\r\n    text-align: center;\r\n    letter-spacing: 0.5em;\r\n    padding: var(--spacing-md);\r\n  }\r\n\r\n  .mat-mdc-form-field-label {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  &.mat-focused {\r\n    .mat-mdc-form-field-label {\r\n      color: var(--accent-green);\r\n    }\r\n\r\n    .mat-mdc-text-field-wrapper {\r\n      border-color: var(--accent-green);\r\n      box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);\r\n    }\r\n  }\r\n\r\n  &.mat-form-field-invalid {\r\n    .mat-mdc-text-field-wrapper {\r\n      border-color: var(--error);\r\n      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.otp-input-single {\r\n  text-align: center;\r\n  letter-spacing: 0.3em;\r\n  font-size: var(--font-size-xl);\r\n  font-weight: 600;\r\n}\r\n\r\n/* Verify Button */\r\n.verify-button {\r\n  height: 48px;\r\n  font-size: var(--font-size-base);\r\n  font-weight: 500;\r\n  text-transform: none;\r\n  border-radius: var(--radius-md);\r\n  margin-bottom: var(--spacing-xl);\r\n  position: relative;\r\n\r\n  &:disabled {\r\n    opacity: 0.6;\r\n  }\r\n\r\n  .button-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: var(--spacing-sm);\r\n    position: relative;\r\n  }\r\n\r\n  .hidden {\r\n    visibility: hidden;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.button-spinner {\r\n  position: absolute;\r\n\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* Resend Section */\r\n.resend-section {\r\n  text-align: center;\r\n  margin-bottom: var(--spacing-lg);\r\n}\r\n\r\n.resend-text {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n.resend-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-xs);\r\n  font-size: var(--font-size-sm);\r\n  text-transform: none;\r\n  margin: 0 auto;\r\n  position: relative;\r\n\r\n  &:not(.disabled) {\r\n    color: var(--accent-green) !important;\r\n\r\n    &:hover {\r\n      color: var(--accent-green-hover) !important;\r\n      background: transparent;\r\n    }\r\n  }\r\n\r\n  &.disabled {\r\n    color: var(--text-disabled) !important;\r\n    cursor: not-allowed;\r\n  }\r\n\r\n  .button-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: var(--spacing-xs);\r\n    position: relative;\r\n  }\r\n\r\n  .hidden {\r\n    visibility: hidden;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.resend-spinner {\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* Back to Login */\r\n.back-to-login {\r\n  text-align: center;\r\n  padding-top: var(--spacing-lg);\r\n}\r\n\r\n.back-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-xs);\r\n  color: var(--accent-green) !important;\r\n  text-transform: none;\r\n  font-size: var(--font-size-sm);\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    color: var(--accent-green-hover) !important;\r\n    background: transparent;\r\n  }\r\n}\r\n\r\n/* Snackbar Styles */\r\n::ng-deep .error-snackbar {\r\n  background-color: var(--error) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n::ng-deep .success-snackbar {\r\n  background-color: var(--success) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 480px) {\r\n  .otp-card {\r\n    margin: var(--spacing-sm);\r\n    max-width: calc(100vw - 2rem);\r\n  }\r\n\r\n  .auth-header {\r\n    padding: var(--spacing-lg) var(--spacing-md);\r\n  }\r\n\r\n  .auth-form {\r\n    padding: var(--spacing-lg) var(--spacing-md);\r\n  }\r\n\r\n  .auth-footer {\r\n    padding: var(--spacing-md);\r\n  }\r\n\r\n  .auth-logo {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n\r\n  .auth-title {\r\n    font-size: var(--font-size-xl);\r\n  }\r\n\r\n  .otp-input-field {\r\n    .mat-mdc-form-field-input {\r\n      font-size: var(--font-size-lg);\r\n      letter-spacing: 0.2em;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 360px) {\r\n  .otp-input-field {\r\n    .mat-mdc-form-field-input {\r\n      font-size: var(--font-size-base);\r\n      letter-spacing: 0.1em;\r\n    }\r\n  }\r\n}\r\n\r\n/* Focus and Hover States */\r\n.mat-mdc-button:focus {\r\n  outline: 2px solid var(--accent-green);\r\n  outline-offset: 2px;\r\n}\r\n\r\n/* Loading State Animation */\r\n.button-spinner,\r\n.resend-spinner {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* OTP Input Animation */\r\n.otp-input-field {\r\n  animation: fadeIn 0.3s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Success State Animation */\r\n.otp-input-field.mat-form-field-valid {\r\n  .mat-mdc-text-field-wrapper {\r\n    animation: successPulse 0.6s ease-in-out;\r\n    border-color: var(--success);\r\n    background-color: rgba(16, 185, 129, 0.1);\r\n  }\r\n}\r\n\r\n@keyframes successPulse {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.02);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n/* Timer Display Styling */\r\n.resend-button .mat-icon {\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.5;\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* Logo Fallback Styles */\r\n.logo-fallback {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--accent-green) 0%,\r\n    var(--accent-green-light) 100%\r\n  );\r\n  border-radius: 50%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n"], "mappings": ";AAEA,CAAA;AACE,aAAA;;AAGF,CAAA;AACE,SAAA;;AAIF,CAAA;AACE,iBAAA,IAAA;;AAGF,CAAA;AACE,SAAA;;AAEA,CAHF,gBAGE,CAAA;AACE,oBAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,CARF,gBAQE,CAAA;AACE,SAAA,IAAA;AACA,aAAA,IAAA;AACA,eAAA;AACA,cAAA;AACA,kBAAA;AACA,WAAA,IAAA;;AAGF,CAjBF,gBAiBE,CAAA;AACE,SAAA,IAAA;;AAIA,CAtBJ,eAsBI,CAAA,YAAA,CALF;AAMI,SAAA,IAAA;;AAGF,CA1BJ,eA0BI,CAJA,YAIA,CAvBF;AAwBI,gBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAKF,CAjCJ,eAiCI,CAAA,uBAAA,CA9BF;AA+BI,gBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;;AAKN,CAAA;AACE,cAAA;AACA,kBAAA;AACA,aAAA,IAAA;AACA,eAAA;;AAIF,CAAA;AACE,UAAA;AACA,aAAA,IAAA;AACA,eAAA;AACA,kBAAA;AACA,iBAAA,IAAA;AACA,iBAAA,IAAA;AACA,YAAA;;AAEA,CATF,aASE;AACE,WAAA;;AAGF,CAbF,cAaE,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,YAAA;;AAGF,CArBF,cAqBE,CAAA;AACE,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,YAAA;AAEA,aAAA,UAAA,IAAA,EAAA;;AAIF,CAAA;AACE,cAAA;AACA,iBAAA,IAAA;;AAGF,CAAA;AACE,SAAA,IAAA;AACA,aAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA,IAAA;AACA,aAAA,IAAA;AACA,kBAAA;AACA,UAAA,EAAA;AACA,YAAA;;AAEA,CATF,aASE,KAAA,CAAA;AACE,SAAA,IAAA;;AAEA,CAZJ,aAYI,KAAA,CAHF,SAGE;AACE,SAAA,IAAA;AACA,cAAA;;AAIJ,CAlBF,aAkBE,CATA;AAUE,SAAA,IAAA;AACA,UAAA;;AAGF,CAvBF,cAuBE,CAvDA;AAwDE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,YAAA;;AAGF,CA/BF,cA+BE,CAvDA;AAwDE,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,YAAA;AACA,QAAA;AACA,OAAA;AACA,aAAA,UAAA,IAAA,EAAA;;AAIF,CAAA;AACE,cAAA;AACA,eAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA,IAAA;AACA,SAAA,IAAA;AACA,kBAAA;AACA,aAAA,IAAA;AACA,UAAA,EAAA;;AAEA,CATF,WASE;AACE,SAAA,IAAA;AACA,cAAA;;AAKJ,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAGF,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAIF,OAAA,CAAA,SAAA,EAAA;AACE,GAxLF;AAyLI,YAAA,IAAA;AACA,eAAA,KAAA,MAAA,EAAA;;AAGF,GAAA;AACE,aAAA,IAAA,cAAA,IAAA;;AAGF,GAAA;AACE,aAAA,IAAA,cAAA,IAAA;;AAGF,GAAA;AACE,aAAA,IAAA;;AAGF,GAAA;AACE,WAAA;AACA,YAAA;;AAGF,GAAA;AACE,eAAA,IAAA;;AAIA,GAtMJ,gBAsMI,CA9LF;AA+LI,eAAA,IAAA;AACA,oBAAA;;;AAKN,OAAA,CAAA,SAAA,EAAA;AAEI,GA/MJ,gBA+MI,CAvMF;AAwMI,eAAA,IAAA;AACA,oBAAA;;;AAMN,CAAA,cAAA;AACE,WAAA,IAAA,MAAA,IAAA;AACA,kBAAA;;AAIF,CAlJA;AAkJA,CA3FA;AA6FE,aAAA,KAAA,GAAA,OAAA;;AAGF,WAHE;AAIA;AACE,eAAA,OAAA;;AAEF;AACE,eAAA,OAAA;;;AAKJ,CA5OA;AA6OE,aAAA,OAAA,KAAA;;AAGF,WAHE;AAIA;AACE,aAAA;AACA,eAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA;;;AAMF,CA7PF,eA6PE,CAAA,qBAAA,CA1PA;AA2PE,aAAA,aAAA,KAAA;AACA,gBAAA,IAAA;AACA,oBAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIJ,WANI;AAOF;AACE,eAAA,MAAA;;AAEF;AACE,eAAA,MAAA;;AAEF;AACE,eAAA,MAAA;;;AAKJ,CApLA,cAoLA,CAAA;AACE,aAAA,MAAA,GAAA;;AAGF,WAHE;AAIA;AACE,aAAA;;AAEF;AACE,aAAA;;AAEF;AACE,aAAA;;;AAKJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA;IAAA;MAAA,MAAA;MAAA,IAAA,gBAAA,EAAA;MAAA,IAAA,sBAAA;AAKA,iBAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,eAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;", "names": []}
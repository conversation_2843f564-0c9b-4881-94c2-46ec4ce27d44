{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/platform-DmdVEw_C.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/style-loader-Cu9AvjH9.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/backwards-compatibility-DHR38MsD.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/element-x4z00URv.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Injectable } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n  _platformId = inject(PLATFORM_ID);\n  // We want to use the Angular platform check because if the Document is shimmed\n  // without the navigator, the following checks will fail. This is preferred because\n  // sometimes the Document may be shimmed without the user's knowledge or intention\n  /** Whether the Angular application is being rendered in the browser. */\n  isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n  /** Whether the current browser is Microsoft Edge. */\n  EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n  /** Whether the current rendering engine is Microsoft Trident. */\n  TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n  // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n  /** Whether the current rendering engine is Blink. */\n  BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT;\n  // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n  // ensure that Webkit runs standalone and is not used as another engine's base.\n  /** Whether the current rendering engine is WebKit. */\n  WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n  /** Whether the current platform is Apple iOS. */\n  IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n  // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n  // them self as Gecko-like browsers and modify the userAgent's according to that.\n  // Since we only cover one explicit Firefox case, we can simply check for Firefox\n  // instead of having an unstable check for Gecko.\n  /** Whether the current browser is Firefox. */\n  FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n  /** Whether the current platform is Android. */\n  // Trident on mobile adds the android platform to the userAgent to trick detections.\n  ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n  // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n  // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n  // Safari browser should also use Webkit as its layout engine.\n  /** Whether the current browser is Safari. */\n  SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n  constructor() {}\n  static ɵfac = function Platform_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Platform)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { Platform as P };\n", "import * as i0 from '@angular/core';\nimport { inject, Injector, EnvironmentInjector, ApplicationRef, createComponent, Injectable } from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = new WeakMap();\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\nclass _CdkPrivateStyleLoader {\n  _appRef;\n  _injector = inject(Injector);\n  _environmentInjector = inject(EnvironmentInjector);\n  /**\n   * Loads a set of styles.\n   * @param loader Component which will be instantiated to load the styles.\n   */\n  load(loader) {\n    // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n    const appRef = this._appRef = this._appRef || this._injector.get(ApplicationRef);\n    let data = appsWithLoaders.get(appRef);\n    // If we haven't loaded for this app before, we have to initialize it.\n    if (!data) {\n      data = {\n        loaders: new Set(),\n        refs: []\n      };\n      appsWithLoaders.set(appRef, data);\n      // When the app is destroyed, we need to clean up all the related loaders.\n      appRef.onDestroy(() => {\n        appsWithLoaders.get(appRef)?.refs.forEach(ref => ref.destroy());\n        appsWithLoaders.delete(appRef);\n      });\n    }\n    // If the loader hasn't been loaded before, we need to instatiate it.\n    if (!data.loaders.has(loader)) {\n      data.loaders.add(loader);\n      data.refs.push(createComponent(loader, {\n        environmentInjector: this._environmentInjector\n      }));\n    }\n  }\n  static ɵfac = function _CdkPrivateStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkPrivateStyleLoader)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _CdkPrivateStyleLoader,\n    factory: _CdkPrivateStyleLoader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkPrivateStyleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _CdkPrivateStyleLoader as _ };\n", "import { VERSION } from '@angular/core';\n\n// TODO(crisbeto): remove this function when making breaking changes for v20.\n/**\n * Binds an event listener with specific options in a backwards-compatible way.\n * This function is necessary, because `Renderer2.listen` only supports listener options\n * after 19.1 and during the v19 period we support any 19.x version.\n * @docs-private\n */\nfunction _bindEventWithOptions(renderer, target, eventName, callback, options) {\n  const major = parseInt(VERSION.major);\n  const minor = parseInt(VERSION.minor);\n  // Event options in `listen` are only supported in 19.1 and beyond.\n  // We also allow 0.0.x, because that indicates a build at HEAD.\n  if (major > 19 || major === 19 && minor > 0 || major === 0 && minor === 0) {\n    return renderer.listen(target, eventName, callback, options);\n  }\n  target.addEventListener(eventName, callback, options);\n  return () => {\n    target.removeEventListener(eventName, callback, options);\n  };\n}\nexport { _bindEventWithOptions as _ };\n", "import { ElementRef } from '@angular/core';\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  if (_isNumberValue(value)) {\n    return Number(value);\n  }\n  return arguments.length === 2 ? fallbackValue : 0;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\nexport { _isNumberValue as _, coerceElement as a, coerceNumberProperty as c };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAMA,IAAI;AAMJ,IAAI;AACF,uBAAqB,OAAO,SAAS,eAAe,KAAK;AAC3D,QAAQ;AACN,uBAAqB;AACvB;AAKA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,cAAc,OAAO,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,YAAY,KAAK,cAAc,kBAAkB,KAAK,WAAW,IAAI,OAAO,aAAa,YAAY,CAAC,CAAC;AAAA;AAAA,EAEvG,OAAO,KAAK,aAAa,UAAU,KAAK,UAAU,SAAS;AAAA;AAAA,EAE3D,UAAU,KAAK,aAAa,kBAAkB,KAAK,UAAU,SAAS;AAAA;AAAA;AAAA,EAGtE,QAAQ,KAAK,aAAa,CAAC,EAAE,OAAO,UAAU,uBAAuB,OAAO,QAAQ,eAAe,CAAC,KAAK,QAAQ,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA,EAIvH,SAAS,KAAK,aAAa,eAAe,KAAK,UAAU,SAAS,KAAK,CAAC,KAAK,SAAS,CAAC,KAAK,QAAQ,CAAC,KAAK;AAAA;AAAA,EAE1G,MAAM,KAAK,aAAa,mBAAmB,KAAK,UAAU,SAAS,KAAK,EAAE,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxF,UAAU,KAAK,aAAa,uBAAuB,KAAK,UAAU,SAAS;AAAA;AAAA;AAAA,EAG3E,UAAU,KAAK,aAAa,WAAW,KAAK,UAAU,SAAS,KAAK,CAAC,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1E,SAAS,KAAK,aAAa,UAAU,KAAK,UAAU,SAAS,KAAK,KAAK;AAAA,EACvE,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,UAAS;AAAA,IAClB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACpEH,IAAM,kBAAkB,oBAAI,QAAQ;AAKpC,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,uBAAuB,OAAO,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjD,KAAK,QAAQ;AAEX,UAAM,SAAS,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,IAAI,cAAc;AAC/E,QAAI,OAAO,gBAAgB,IAAI,MAAM;AAErC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,QACL,SAAS,oBAAI,IAAI;AAAA,QACjB,MAAM,CAAC;AAAA,MACT;AACA,sBAAgB,IAAI,QAAQ,IAAI;AAEhC,aAAO,UAAU,MAAM;AACrB,wBAAgB,IAAI,MAAM,GAAG,KAAK,QAAQ,SAAO,IAAI,QAAQ,CAAC;AAC9D,wBAAgB,OAAO,MAAM;AAAA,MAC/B,CAAC;AAAA,IACH;AAEA,QAAI,CAAC,KAAK,QAAQ,IAAI,MAAM,GAAG;AAC7B,WAAK,QAAQ,IAAI,MAAM;AACvB,WAAK,KAAK,KAAK,gBAAgB,QAAQ;AAAA,QACrC,qBAAqB,KAAK;AAAA,MAC5B,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,wBAAuB;AAAA,IAChC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACjDH,SAAS,sBAAsB,UAAU,QAAQ,WAAW,UAAU,SAAS;AAC7E,QAAM,QAAQ,SAAS,QAAQ,KAAK;AACpC,QAAM,QAAQ,SAAS,QAAQ,KAAK;AAGpC,MAAI,QAAQ,MAAM,UAAU,MAAM,QAAQ,KAAK,UAAU,KAAK,UAAU,GAAG;AACzE,WAAO,SAAS,OAAO,QAAQ,WAAW,UAAU,OAAO;AAAA,EAC7D;AACA,SAAO,iBAAiB,WAAW,UAAU,OAAO;AACpD,SAAO,MAAM;AACX,WAAO,oBAAoB,WAAW,UAAU,OAAO;AAAA,EACzD;AACF;;;ACpBA,SAAS,qBAAqB,OAAO,gBAAgB,GAAG;AACtD,MAAI,eAAe,KAAK,GAAG;AACzB,WAAO,OAAO,KAAK;AAAA,EACrB;AACA,SAAO,UAAU,WAAW,IAAI,gBAAgB;AAClD;AAKA,SAAS,eAAe,OAAO;AAI7B,SAAO,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC;AAC1D;AAMA,SAAS,cAAc,cAAc;AACnC,SAAO,wBAAwB,aAAa,aAAa,gBAAgB;AAC3E;", "names": []}
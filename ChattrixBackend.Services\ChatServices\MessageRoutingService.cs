using ChattrixBackend.Core.Entities.ChatManagement.MessageModel;
using ChattrixBackend.Core.Entities.ChatManagement.WebSocketModel;
using ChattrixBackend.EntityFramworkCore.Data;
using ChattrixBackend.Services.WebSocketServices;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ChattrixBackend.Services.ChatServices {
    public class MessageRoutingService : IMessageRoutingService {
        private readonly IWebSocketConnectionManager _connectionManager;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<MessageRoutingService> _logger;

        public MessageRoutingService(
            IWebSocketConnectionManager connectionManager,
            ApplicationDbContext context,
            ILogger<MessageRoutingService> logger) {
            _connectionManager = connectionManager;
            _context = context;
            _logger = logger;
        }

        public async Task RouteMessageAsync(Message message) {
            try {
                // Get sender details
                var sender = await _context.Users.FindAsync(message.SenderId);
                if (sender == null) return;

                // Get conversation participants
                var participants = await _context.ConversationParticipants
                    .Where(cp => cp.ConversationId == message.ConversationId && cp.IsActive)
                    .Include(cp => cp.User)
                    .ToListAsync();

                var messageData = new MessageDeliveredResponse {
                    MessageId = message.Id,
                    ConversationId = message.ConversationId,
                    SenderId = message.SenderId,
                    SenderName = sender.FullName,
                    Content = message.Content,
                    Type = message.Type.ToString().ToLower(),
                    SentAt = message.SentAt,
                    ReplyToMessageId = message.ReplyToMessageId,
                    ClientMessageId = message.ClientMessageId
                };

                var wsMessage = new WebSocketMessage {
                    Type = "message_received",
                    Data = messageData
                };

                // Send to all participants except sender
                var recipientIds = participants
                    .Where(p => p.UserId != message.SenderId)
                    .Select(p => p.UserId)
                    .ToList();

                foreach (var recipientId in recipientIds) {
                    await _connectionManager.SendToUserAsync(recipientId, wsMessage);
                }

                // Update delivery status for online recipients
                await UpdateDeliveryStatusForOnlineUsers(message.Id, recipientIds);

                _logger.LogInformation($"Message {message.Id} routed to {recipientIds.Count} recipients");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error routing message {message.Id}");
            }
        }

        public async Task RouteMessageStatusUpdateAsync(string messageId, string userId, DeliveryStatus status) {
            try {
                // Get message details
                var message = await _context.Messages
                    .Include(m => m.Sender)
                    .FirstOrDefaultAsync(m => m.Id == messageId);

                if (message == null) return;

                // Get user details
                var user = await _context.Users.FindAsync(userId);
                if (user == null) return;

                var statusUpdateData = new {
                    messageId = messageId,
                    userId = userId,
                    userName = user.FullName,
                    status = status.ToString().ToLower(),
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "message_status_updated",
                    Data = statusUpdateData
                };

                // Notify the message sender
                await _connectionManager.SendToUserAsync(message.SenderId, wsMessage);

                // For read receipts, also notify other conversation participants
                if (status == DeliveryStatus.Read) {
                    await _connectionManager.SendToConversationAsync(
                        message.ConversationId,
                        new WebSocketMessage {
                            Type = "message_read",
                            Data = new {
                                messageId = messageId,
                                readBy = userId,
                                readByName = user.FullName,
                                readAt = DateTime.UtcNow
                            }
                        },
                        userId // Exclude the user who read the message
                    );
                }

                _logger.LogInformation($"Message status update routed: {messageId} - {status} by {userId}");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error routing message status update for message {messageId}");
            }
        }

        public async Task RouteTypingIndicatorAsync(string conversationId, string userId, bool isTyping) {
            try {
                var user = await _context.Users.FindAsync(userId);
                if (user == null) return;

                var typingData = new {
                    conversationId = conversationId,
                    userId = userId,
                    userName = user.FullName,
                    isTyping = isTyping,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "user_typing",
                    Data = typingData
                };

                // Send to all conversation participants except the typing user
                await _connectionManager.SendToConversationAsync(conversationId, wsMessage, userId);

                _logger.LogDebug($"Typing indicator routed: {userId} - {isTyping} in {conversationId}");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error routing typing indicator for user {userId} in conversation {conversationId}");
            }
        }

        public async Task RoutePresenceUpdateAsync(string userId, string status) {
            try {
                var user = await _context.Users.FindAsync(userId);
                if (user == null) return;

                var presenceData = new {
                    userId = userId,
                    userName = user.FullName,
                    status = status,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "user_presence_updated",
                    Data = presenceData
                };

                // Get all conversations where this user is a participant
                var conversationIds = await _context.ConversationParticipants
                    .Where(cp => cp.UserId == userId && cp.IsActive)
                    .Select(cp => cp.ConversationId)
                    .ToListAsync();

                // Notify all participants in those conversations
                foreach (var conversationId in conversationIds) {
                    await _connectionManager.SendToConversationAsync(conversationId, wsMessage, userId);
                }

                _logger.LogInformation($"Presence update routed: {userId} - {status}");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error routing presence update for user {userId}");
            }
        }

        public async Task RouteConversationUpdateAsync(string conversationId, object updateData) {
            try {
                var wsMessage = new WebSocketMessage {
                    Type = "conversation_updated",
                    Data = updateData
                };

                await _connectionManager.SendToConversationAsync(conversationId, wsMessage);

                _logger.LogInformation($"Conversation update routed: {conversationId}");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error routing conversation update for {conversationId}");
            }
        }

        public async Task RouteParticipantUpdateAsync(string conversationId, string participantId, string action) {
            try {
                var participant = await _context.Users.FindAsync(participantId);
                if (participant == null) return;

                var participantData = new {
                    conversationId = conversationId,
                    participantId = participantId,
                    participantName = participant.FullName,
                    action = action,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "participant_updated",
                    Data = participantData
                };

                await _connectionManager.SendToConversationAsync(conversationId, wsMessage);

                _logger.LogInformation($"Participant update routed: {participantId} - {action} in {conversationId}");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error routing participant update for {participantId} in conversation {conversationId}");
            }
        }

        public async Task NotifyMessageDeliveryAsync(string messageId, List<string> participantIds) {
            try {
                var deliveryData = new {
                    messageId = messageId,
                    deliveredTo = participantIds.Count,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "message_delivered",
                    Data = deliveryData
                };

                // Get message sender
                var message = await _context.Messages.FindAsync(messageId);
                if (message != null) {
                    await _connectionManager.SendToUserAsync(message.SenderId, wsMessage);
                }

                _logger.LogInformation($"Message delivery notification sent for message {messageId}");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error notifying message delivery for message {messageId}");
            }
        }

        public async Task NotifyMessageReadAsync(string messageId, string userId) {
            try {
                var message = await _context.Messages
                    .Include(m => m.Sender)
                    .FirstOrDefaultAsync(m => m.Id == messageId);

                if (message == null) return;

                var user = await _context.Users.FindAsync(userId);
                if (user == null) return;

                var readData = new {
                    messageId = messageId,
                    readBy = userId,
                    readByName = user.FullName,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "message_read_receipt",
                    Data = readData
                };

                // Notify the message sender
                await _connectionManager.SendToUserAsync(message.SenderId, wsMessage);

                _logger.LogInformation($"Message read notification sent for message {messageId} read by {userId}");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error notifying message read for message {messageId}");
            }
        }

        public async Task BroadcastSystemMessageAsync(string conversationId, string systemMessage) {
            try {
                var systemData = new {
                    conversationId = conversationId,
                    message = systemMessage,
                    timestamp = DateTime.UtcNow
                };

                var wsMessage = new WebSocketMessage {
                    Type = "system_message",
                    Data = systemData
                };

                await _connectionManager.SendToConversationAsync(conversationId, wsMessage);

                _logger.LogInformation($"System message broadcasted to conversation {conversationId}");
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error broadcasting system message to conversation {conversationId}");
            }
        }

        private async Task UpdateDeliveryStatusForOnlineUsers(string messageId, List<string> recipientIds) {
            try {
                var onlineRecipients = new List<string>();

                foreach (var recipientId in recipientIds) {
                    if (await _connectionManager.IsUserOnlineAsync(recipientId)) {
                        onlineRecipients.Add(recipientId);

                        // Update message status to delivered
                        var messageStatus = await _context.MessageStatuses
                            .FirstOrDefaultAsync(ms => ms.MessageId == messageId && ms.UserId == recipientId);

                        if (messageStatus != null) {
                            messageStatus.Status = DeliveryStatus.Delivered;
                            messageStatus.StatusAt = DateTime.UtcNow;
                        }
                    }
                }

                if (onlineRecipients.Any()) {
                    await _context.SaveChangesAsync();
                    await NotifyMessageDeliveryAsync(messageId, onlineRecipients);
                }
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error updating delivery status for message {messageId}");
            }
        }
    }
}

using System.ComponentModel.DataAnnotations;
using ChattrixBackend.Core.Entities.UserManagement.UserModel;

namespace ChattrixBackend.Core.Entities.ChatManagement.ConversationModel {
    public class Conversation {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        public ConversationType Type { get; set; }

        [StringLength(500)]
        public string? GroupImageUrl { get; set; }

        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        public DateTime? LastMessageAt { get; set; }

        public string? LastMessageId { get; set; }

        public bool IsActive { get; set; } = true;

        // For group chats - admin management
        public bool IsAdminOnly { get; set; } = false;

        // Navigation properties
        public virtual ApplicationUser Creator { get; set; } = null!;
        public virtual ICollection<ConversationParticipant> Participants { get; set; } = new List<ConversationParticipant>();
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
        public virtual Message? LastMessage { get; set; }
    }

    public enum ConversationType {
        Private = 1,
        Group = 2
    }
}

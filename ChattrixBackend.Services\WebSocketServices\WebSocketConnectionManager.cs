using ChattrixBackend.Core.Entities.ChatManagement.WebSocketModel;
using ChattrixBackend.EntityFramworkCore.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Net.WebSockets;

namespace ChattrixBackend.Services.WebSocketServices {
    public class WebSocketConnectionManager : IWebSocketConnectionManager {
        private readonly ConcurrentDictionary<string, WebSocketConnection> _connections = new();
        private readonly ConcurrentDictionary<string, HashSet<string>> _userConnections = new();
        private readonly ApplicationDbContext _context;
        private readonly ILogger<WebSocketConnectionManager> _logger;

        public WebSocketConnectionManager(ApplicationDbContext context, ILogger<WebSocketConnectionManager> logger) {
            _context = context;
            _logger = logger;
        }

        public async Task<string> AddConnectionAsync(WebSocket webSocket, string? ipAddress = null, string? deviceInfo = null) {
            var connection = new WebSocketConnection {
                WebSocket = webSocket,
                IpAddress = ipAddress,
                DeviceInfo = deviceInfo
            };

            _connections.TryAdd(connection.Id, connection);
            _logger.LogInformation($"WebSocket connection added: {connection.Id}");

            return connection.Id;
        }

        public async Task<bool> AuthenticateConnectionAsync(string connectionId, string userId) {
            if (_connections.TryGetValue(connectionId, out var connection)) {
                connection.UserId = userId;
                connection.IsAuthenticated = true;

                // Add to user connections mapping
                _userConnections.AddOrUpdate(userId, 
                    new HashSet<string> { connectionId },
                    (key, existing) => {
                        existing.Add(connectionId);
                        return existing;
                    });

                // Update user online status
                await UpdateUserOnlineStatusAsync(userId, true, connectionId);

                _logger.LogInformation($"WebSocket connection authenticated: {connectionId} for user: {userId}");
                return true;
            }

            return false;
        }

        public async Task RemoveConnectionAsync(string connectionId) {
            if (_connections.TryRemove(connectionId, out var connection)) {
                if (connection.IsAuthenticated && !string.IsNullOrEmpty(connection.UserId)) {
                    // Remove from user connections mapping
                    if (_userConnections.TryGetValue(connection.UserId, out var userConnections)) {
                        userConnections.Remove(connectionId);
                        if (userConnections.Count == 0) {
                            _userConnections.TryRemove(connection.UserId, out _);
                            // Update user offline status
                            await UpdateUserOnlineStatusAsync(connection.UserId, false);
                        }
                    }
                }

                await connection.CloseAsync();
                _logger.LogInformation($"WebSocket connection removed: {connectionId}");
            }
        }

        public async Task<WebSocketConnection?> GetConnectionAsync(string connectionId) {
            _connections.TryGetValue(connectionId, out var connection);
            return connection;
        }

        public async Task<List<WebSocketConnection>> GetUserConnectionsAsync(string userId) {
            if (_userConnections.TryGetValue(userId, out var connectionIds)) {
                return connectionIds
                    .Select(id => _connections.TryGetValue(id, out var conn) ? conn : null)
                    .Where(conn => conn != null)
                    .ToList()!;
            }
            return new List<WebSocketConnection>();
        }

        public async Task<List<WebSocketConnection>> GetConversationConnectionsAsync(string conversationId) {
            // Get all participants of the conversation
            var participantUserIds = await _context.ConversationParticipants
                .Where(cp => cp.ConversationId == conversationId && cp.IsActive)
                .Select(cp => cp.UserId)
                .ToListAsync();

            var connections = new List<WebSocketConnection>();
            foreach (var userId in participantUserIds) {
                var userConnections = await GetUserConnectionsAsync(userId);
                connections.AddRange(userConnections);
            }

            return connections;
        }

        public async Task SendToConnectionAsync(string connectionId, object message) {
            if (_connections.TryGetValue(connectionId, out var connection)) {
                try {
                    await connection.SendAsync(message);
                } catch (Exception ex) {
                    _logger.LogError(ex, $"Error sending message to connection {connectionId}");
                    await RemoveConnectionAsync(connectionId);
                }
            }
        }

        public async Task SendToUserAsync(string userId, object message) {
            var connections = await GetUserConnectionsAsync(userId);
            var tasks = connections.Select(conn => SendToConnectionAsync(conn.Id, message));
            await Task.WhenAll(tasks);
        }

        public async Task SendToConversationAsync(string conversationId, object message, string? excludeUserId = null) {
            var connections = await GetConversationConnectionsAsync(conversationId);
            var filteredConnections = excludeUserId != null 
                ? connections.Where(c => c.UserId != excludeUserId) 
                : connections;

            var tasks = filteredConnections.Select(conn => SendToConnectionAsync(conn.Id, message));
            await Task.WhenAll(tasks);
        }

        public async Task BroadcastAsync(object message, string? excludeUserId = null) {
            var connections = excludeUserId != null 
                ? _connections.Values.Where(c => c.UserId != excludeUserId) 
                : _connections.Values;

            var tasks = connections.Select(conn => SendToConnectionAsync(conn.Id, message));
            await Task.WhenAll(tasks);
        }

        public async Task<bool> IsUserOnlineAsync(string userId) {
            return _userConnections.ContainsKey(userId);
        }

        public async Task<int> GetActiveConnectionsCountAsync() {
            return _connections.Count;
        }

        public async Task<List<string>> GetOnlineUsersAsync() {
            return _userConnections.Keys.ToList();
        }

        public async Task CleanupInactiveConnectionsAsync() {
            var inactiveConnections = _connections.Values
                .Where(c => c.WebSocket.State != WebSocketState.Open)
                .ToList();

            foreach (var connection in inactiveConnections) {
                await RemoveConnectionAsync(connection.Id);
            }
        }

        private async Task UpdateUserOnlineStatusAsync(string userId, bool isOnline, string? connectionId = null) {
            try {
                var user = await _context.Users.FindAsync(userId);
                if (user != null) {
                    user.IsOnline = isOnline;
                    user.LastSeen = DateTime.UtcNow;
                    user.CurrentConnectionId = isOnline ? connectionId : null;

                    // Add presence record
                    var presence = new Core.Entities.ChatManagement.PresenceModel.UserPresence {
                        UserId = userId,
                        Status = isOnline 
                            ? Core.Entities.ChatManagement.PresenceModel.PresenceStatus.Online 
                            : Core.Entities.ChatManagement.PresenceModel.PresenceStatus.Offline,
                        ConnectionId = connectionId
                    };

                    _context.UserPresences.Add(presence);
                    await _context.SaveChangesAsync();
                }
            } catch (Exception ex) {
                _logger.LogError(ex, $"Error updating user online status for user {userId}");
            }
        }
    }
}

{"version": 3, "sources": ["src/app/Pages/authentication/Pages/sign-up/sign-up.component.scss"], "sourcesContent": ["/* Signup Component Specific Styles */\r\n\r\n.signup-card {\r\n  max-width: 480px; // Slightly wider for signup form\r\n\r\n  // Specific optimizations for signup form\r\n  .auth-header {\r\n    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-xs);\r\n  }\r\n\r\n  .auth-form {\r\n    padding: var(--spacing-sm) var(--spacing-lg);\r\n  }\r\n\r\n  .auth-footer {\r\n    padding: var(--spacing-sm) var(--spacing-lg);\r\n  }\r\n\r\n  .auth-logo {\r\n    width: 50px;\r\n    height: 50px;\r\n    margin-bottom: var(--spacing-sm);\r\n  }\r\n\r\n  .auth-title {\r\n    font-size: var(--font-size-lg);\r\n    margin-bottom: var(--spacing-xs);\r\n  }\r\n\r\n  .auth-subtitle {\r\n    font-size: var(--font-size-xs);\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.full-width {\r\n  width: 100%;\r\n  margin-bottom: var(--spacing-sm);\r\n}\r\n\r\n.signup-button {\r\n  height: 44px;\r\n  font-size: var(--font-size-base);\r\n  font-weight: 500;\r\n  text-transform: none;\r\n  border-radius: var(--radius-md);\r\n  margin-bottom: var(--spacing-sm);\r\n  position: relative;\r\n\r\n  &:disabled {\r\n    opacity: 0.6;\r\n  }\r\n\r\n  .button-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: var(--spacing-sm);\r\n    position: relative;\r\n  }\r\n\r\n  .hidden {\r\n    visibility: hidden;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.button-spinner {\r\n  position: absolute;\r\n\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n.login-prompt {\r\n  margin: var(--spacing-sm) 0 0;\r\n  text-align: center;\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n}\r\n\r\n.login-link {\r\n  color: var(--accent-green) !important;\r\n  text-transform: none;\r\n  font-weight: 500;\r\n  padding: 0;\r\n  min-width: auto;\r\n  margin-left: var(--spacing-xs);\r\n\r\n  &:hover {\r\n    color: var(--accent-green-hover) !important;\r\n    background: transparent;\r\n  }\r\n}\r\n\r\n/* Password Strength Indicator */\r\n.password-strength {\r\n  margin-top: var(--spacing-sm);\r\n  padding: var(--spacing-sm);\r\n  background: var(--bg-tertiary);\r\n  border-radius: var(--radius-sm);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n.strength-requirement {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: var(--spacing-xs);\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .requirement-icon {\r\n    width: 16px;\r\n    height: 16px;\r\n    margin-right: var(--spacing-xs);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 10px;\r\n\r\n    &.met {\r\n      background-color: var(--success);\r\n      color: white;\r\n    }\r\n\r\n    &.unmet {\r\n      background-color: var(--text-disabled);\r\n      color: white;\r\n    }\r\n  }\r\n\r\n  .requirement-text {\r\n    &.met {\r\n      color: var(--success);\r\n    }\r\n\r\n    &.unmet {\r\n      color: var(--text-disabled);\r\n    }\r\n  }\r\n}\r\n\r\n/* Material UI Form Field Customizations for Signup */\r\n::ng-deep .signup-card .mat-mdc-form-field {\r\n  .mat-mdc-text-field-wrapper {\r\n    background-color: var(--bg-input);\r\n    border-radius: var(--radius-md);\r\n  }\r\n\r\n  .mat-mdc-form-field-label {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .mat-mdc-form-field-input {\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  .mat-mdc-form-field-icon-suffix {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  &.mat-focused {\r\n    .mat-mdc-form-field-label {\r\n      color: var(--accent-green);\r\n    }\r\n  }\r\n\r\n  // Compact form field wrapper for signup\r\n  .mat-mdc-form-field-wrapper {\r\n    padding-bottom: 0;\r\n  }\r\n\r\n  .mat-mdc-form-field-subscript-wrapper {\r\n    margin-top: 2px;\r\n    min-height: 14px;\r\n    font-size: var(--font-size-xs);\r\n  }\r\n\r\n  // Textarea specific styles\r\n  textarea.mat-mdc-input-element {\r\n    resize: vertical;\r\n    min-height: 50px;\r\n  }\r\n}\r\n\r\n/* Character counter styling */\r\n::ng-deep .mat-mdc-form-field-hint {\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n/* Snackbar Styles */\r\n::ng-deep .error-snackbar {\r\n  background-color: var(--error) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n::ng-deep .success-snackbar {\r\n  background-color: var(--success) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n/* Signup specific container optimization */\r\n.signup-card .auth-container {\r\n  min-height: 100vh;\r\n  min-height: 100dvh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: var(--spacing-xs);\r\n  box-sizing: border-box;\r\n}\r\n/* Responsive Design for Signup */\r\n@media (max-width: 768px) {\r\n  .signup-card {\r\n    .auth-header {\r\n      padding: var(--spacing-xs) var(--spacing-md) var(--spacing-xs);\r\n    }\r\n\r\n    .auth-form {\r\n      padding: var(--spacing-xs) var(--spacing-md);\r\n    }\r\n\r\n    .auth-footer {\r\n      padding: var(--spacing-xs) var(--spacing-md);\r\n    }\r\n\r\n    .full-width {\r\n      margin-bottom: var(--spacing-xs);\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .signup-card {\r\n    margin: var(--spacing-xs);\r\n    max-width: calc(100vw - 1rem);\r\n\r\n    .auth-header {\r\n      padding: var(--spacing-xs) var(--spacing-sm) var(--spacing-xs);\r\n    }\r\n\r\n    .auth-form {\r\n      padding: var(--spacing-xs) var(--spacing-sm);\r\n    }\r\n\r\n    .auth-footer {\r\n      padding: var(--spacing-xs) var(--spacing-sm);\r\n    }\r\n\r\n    .auth-logo {\r\n      width: 40px;\r\n      height: 40px;\r\n      margin-bottom: var(--spacing-xs);\r\n    }\r\n\r\n    .auth-title {\r\n      font-size: var(--font-size-base);\r\n      margin-bottom: 2px;\r\n    }\r\n\r\n    .auth-subtitle {\r\n      font-size: 11px;\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    .full-width {\r\n      margin-bottom: 6px;\r\n    }\r\n\r\n    .signup-button {\r\n      height: 40px;\r\n      margin-bottom: 6px;\r\n    }\r\n\r\n    .login-prompt {\r\n      margin: 6px 0 0;\r\n      font-size: 11px;\r\n    }\r\n  }\r\n}\r\n\r\n/* Focus and Hover States */\r\n.mat-mdc-button:focus {\r\n  outline: 2px solid var(--accent-green);\r\n  outline-offset: 2px;\r\n}\r\n\r\n.mat-mdc-form-field:focus-within {\r\n  .mat-mdc-text-field-wrapper {\r\n    border: 2px solid var(--accent-green);\r\n  }\r\n}\r\n\r\n/* Loading State Animation */\r\n.button-spinner {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Form validation visual feedback */\r\n.mat-mdc-form-field.ng-invalid.ng-touched {\r\n  .mat-mdc-text-field-wrapper {\r\n    border: 1px solid var(--error);\r\n  }\r\n}\r\n\r\n.mat-mdc-form-field.ng-valid.ng-touched {\r\n  .mat-mdc-text-field-wrapper {\r\n    border: 1px solid var(--success);\r\n  }\r\n}\r\n\r\n/* Logo Fallback Styles */\r\n.logo-fallback {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--accent-green) 0%,\r\n    var(--accent-green-light) 100%\r\n  );\r\n  border-radius: 50%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n"], "mappings": ";AAEA,CAAA;AACE,aAAA;;AAGA,CAJF,YAIE,CAAA;AACE,WAAA,IAAA,cAAA,IAAA,cAAA,IAAA;;AAGF,CARF,YAQE,CAAA;AACE,WAAA,IAAA,cAAA,IAAA;;AAGF,CAZF,YAYE,CAAA;AACE,WAAA,IAAA,cAAA,IAAA;;AAGF,CAhBF,YAgBE,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA,IAAA;;AAGF,CAtBF,YAsBE,CAAA;AACE,aAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,CA3BF,YA2BE,CAAA;AACE,aAAA,IAAA;AACA,iBAAA;;AAIJ,CAAA;AACE,SAAA;AACA,iBAAA,IAAA;;AAGF,CAAA;AACE,UAAA;AACA,aAAA,IAAA;AACA,eAAA;AACA,kBAAA;AACA,iBAAA,IAAA;AACA,iBAAA,IAAA;AACA,YAAA;;AAEA,CATF,aASE;AACE,WAAA;;AAGF,CAbF,cAaE,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,YAAA;;AAGF,CArBF,cAqBE,CAAA;AACE,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,YAAA;AAEA,aAAA,UAAA,IAAA,EAAA;;AAGF,CAAA;AACE,UAAA,IAAA,cAAA,EAAA;AACA,cAAA;AACA,SAAA,IAAA;AACA,aAAA,IAAA;;AAGF,CAAA;AACE,SAAA,IAAA;AACA,kBAAA;AACA,eAAA;AACA,WAAA;AACA,aAAA;AACA,eAAA,IAAA;;AAEA,CARF,UAQE;AACE,SAAA,IAAA;AACA,cAAA;;AAKJ,CAAA;AACE,cAAA,IAAA;AACA,WAAA,IAAA;AACA,cAAA,IAAA;AACA,iBAAA,IAAA;AACA,aAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,iBAAA,IAAA;;AAEA,CALF,oBAKE;AACE,iBAAA;;AAGF,CATF,qBASE,CAAA;AACE,SAAA;AACA,UAAA;AACA,gBAAA,IAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;;AAEA,CAnBJ,qBAmBI,CAVF,gBAUE,CAAA;AACE,oBAAA,IAAA;AACA,SAAA;;AAGF,CAxBJ,qBAwBI,CAfF,gBAeE,CAAA;AACE,oBAAA,IAAA;AACA,SAAA;;AAKF,CA/BJ,qBA+BI,CAAA,gBAAA,CAZA;AAaE,SAAA,IAAA;;AAGF,CAnCJ,qBAmCI,CAJA,gBAIA,CAXA;AAYE,SAAA,IAAA;;AAOJ,UAAA,CAhJF,YAgJE,CAAA,mBAAA,CAAA;AACE,oBAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,UAAA,CArJF,YAqJE,CALA,mBAKA,CAAA;AACE,SAAA,IAAA;;AAGF,UAAA,CAzJF,YAyJE,CATA,mBASA,CAAA;AACE,SAAA,IAAA;;AAGF,UAAA,CA7JF,YA6JE,CAbA,mBAaA,CAAA;AACE,SAAA,IAAA;;AAIA,UAAA,CAlKJ,YAkKI,CAlBF,kBAkBE,CAAA,YAAA,CAbF;AAcI,SAAA,IAAA;;AAKJ,UAAA,CAxKF,YAwKE,CAxBA,mBAwBA,CAAA;AACE,kBAAA;;AAGF,UAAA,CA5KF,YA4KE,CA5BA,mBA4BA,CAAA;AACE,cAAA;AACA,cAAA;AACA,aAAA,IAAA;;AAIF,UAAA,CAnLF,YAmLE,CAnCA,mBAmCA,QAAA,CAAA;AACE,UAAA;AACA,cAAA;;AAKJ,UAAA,CAAA;AACE,SAAA,IAAA;AACA,aAAA,IAAA;;AAIF,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAGF,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAIF,CA3MA,YA2MA,CAAA;AACE,cAAA;AACA,cAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA,IAAA;AACA,cAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AAEI,GAvNJ,YAuNI,CAnNF;AAoNI,aAAA,IAAA,cAAA,IAAA,cAAA,IAAA;;AAGF,GA3NJ,YA2NI,CAnNF;AAoNI,aAAA,IAAA,cAAA,IAAA;;AAGF,GA/NJ,YA+NI,CAnNF;AAoNI,aAAA,IAAA,cAAA,IAAA;;AAGF,GAnOJ,YAmOI,CAlMJ;AAmMM,mBAAA,IAAA;;;AAKN,OAAA,CAAA,SAAA,EAAA;AACE,GA1OF;AA2OI,YAAA,IAAA;AACA,eAAA,KAAA,MAAA,EAAA;;AAEA,GA9OJ,YA8OI,CA1OF;AA2OI,aAAA,IAAA,cAAA,IAAA,cAAA,IAAA;;AAGF,GAlPJ,YAkPI,CA1OF;AA2OI,aAAA,IAAA,cAAA,IAAA;;AAGF,GAtPJ,YAsPI,CA1OF;AA2OI,aAAA,IAAA,cAAA,IAAA;;AAGF,GA1PJ,YA0PI,CA1OF;AA2OI,WAAA;AACA,YAAA;AACA,mBAAA,IAAA;;AAGF,GAhQJ,YAgQI,CA1OF;AA2OI,eAAA,IAAA;AACA,mBAAA;;AAGF,GArQJ,YAqQI,CA1OF;AA2OI,eAAA;AACA,mBAAA;;AAGF,GA1QJ,YA0QI,CAzOJ;AA0OM,mBAAA;;AAGF,GA9QJ,YA8QI,CAxOJ;AAyOM,YAAA;AACA,mBAAA;;AAGF,GAnRJ,YAmRI,CA5MJ;AA6MM,YAAA,IAAA,EAAA;AACA,eAAA;;;AAMN,CAAA,cAAA;AACE,WAAA,IAAA,MAAA,IAAA;AACA,kBAAA;;AAIA,CAjJA,kBAiJA,cAAA,CAjJA;AAkJE,UAAA,IAAA,MAAA,IAAA;;AAKJ,CAtOA;AAuOE,aAAA,KAAA,GAAA,OAAA;;AAGF,WAHE;AAIA;AACE,eAAA,OAAA;;AAEF;AACE,eAAA,OAAA;;;AAMF,CAtKA,kBAsKA,CAAA,UAAA,CAAA,WAAA,CAtKA;AAuKE,UAAA,IAAA,MAAA,IAAA;;AAKF,CA5KA,kBA4KA,CAAA,QAAA,CANA,WAMA,CA5KA;AA6KE,UAAA,IAAA,MAAA,IAAA;;AAKJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA;IAAA;MAAA,MAAA;MAAA,IAAA,gBAAA,EAAA;MAAA,IAAA,sBAAA;AAKA,iBAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,eAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;", "names": []}
// User information interface based on JWT token payload
export interface TokenPayload {
  id: string;
  FullName: string;
  Email: string;
  Roles: string | string[];
  IsActive: boolean;
  PhoneNumber?: string;
  Description?: string;
  exp: number; // Token expiration timestamp
  aud: string; // Audience
  iss: string; // Issuer
  nameid: string; // Name identifier
  name: string; // Name
  email: string; // Email
}

// Mapped user information for frontend use
export interface UserInfo {
  id: string;
  name: string;
  email: string;
  role: string | string[];
  isActive: boolean;
  phoneNumber?: string;
  description?: string;
}

// Authentication state interface
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserInfo | null;
  token: string | null;
  error: string | null;
}

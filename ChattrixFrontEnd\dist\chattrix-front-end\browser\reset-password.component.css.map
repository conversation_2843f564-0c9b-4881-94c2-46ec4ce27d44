{"version": 3, "sources": ["src/app/Pages/authentication/Pages/reset-password/reset-password.component.scss"], "sourcesContent": ["/* Reset Password Component Specific Styles */\r\n\r\n.reset-card {\r\n  max-width: 450px; // Slightly wider for password strength indicator\r\n}\r\n\r\n.full-width {\r\n  width: 100%;\r\n  margin-bottom: var(--spacing-md);\r\n}\r\n\r\n.reset-button {\r\n  height: 44px;\r\n  font-size: var(--font-size-base);\r\n  font-weight: 500;\r\n  text-transform: none;\r\n  border-radius: var(--radius-md);\r\n  margin-bottom: var(--spacing-md);\r\n  position: relative;\r\n\r\n  &:disabled {\r\n    opacity: 0.6;\r\n  }\r\n\r\n  .button-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: var(--spacing-sm);\r\n    position: relative;\r\n  }\r\n\r\n  .hidden {\r\n    visibility: hidden;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.button-spinner {\r\n  position: absolute;\r\n\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* Password Strength Indicator */\r\n.password-strength {\r\n  margin-top: var(--spacing-sm);\r\n  margin-bottom: var(--spacing-lg);\r\n  padding: var(--spacing-md);\r\n  background: var(--bg-tertiary);\r\n  border-radius: var(--radius-md);\r\n  border: 1px solid var(--border-primary);\r\n\r\n  h4 {\r\n    font-size: var(--font-size-sm);\r\n    color: var(--text-secondary);\r\n    margin-bottom: var(--spacing-sm);\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.strength-requirement {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: var(--spacing-xs);\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .requirement-icon {\r\n    width: 18px;\r\n    height: 18px;\r\n    margin-right: var(--spacing-sm);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 12px;\r\n    font-weight: bold;\r\n    transition: all var(--transition-normal);\r\n\r\n    &.met {\r\n      background-color: var(--success);\r\n      color: white;\r\n      transform: scale(1);\r\n    }\r\n\r\n    &.unmet {\r\n      background-color: var(--text-disabled);\r\n      color: white;\r\n      transform: scale(0.9);\r\n      opacity: 0.7;\r\n    }\r\n  }\r\n\r\n  .requirement-text {\r\n    font-size: var(--font-size-xs);\r\n    transition: color var(--transition-normal);\r\n\r\n    &.met {\r\n      color: var(--success);\r\n      font-weight: 500;\r\n    }\r\n\r\n    &.unmet {\r\n      color: var(--text-disabled);\r\n    }\r\n  }\r\n}\r\n\r\n/* Back to Login */\r\n.back-to-login {\r\n  text-align: center;\r\n  padding-top: var(--spacing-lg);\r\n}\r\n\r\n.back-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-xs);\r\n  color: var(--accent-green) !important;\r\n  text-transform: none;\r\n  font-size: var(--font-size-sm);\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    color: var(--accent-green-hover) !important;\r\n    background: transparent;\r\n  }\r\n}\r\n\r\n/* Logo Fallback Styles */\r\n.logo-fallback {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: linear-gradient(\r\n    135deg,\r\n    var(--accent-green) 0%,\r\n    var(--accent-green-light) 100%\r\n  );\r\n  border-radius: 50%;\r\n}\r\n\r\n.logo-text {\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: 700;\r\n  color: var(--text-primary);\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* Material UI Form Field Customizations */\r\n::ng-deep .reset-card .mat-mdc-form-field {\r\n  .mat-mdc-text-field-wrapper {\r\n    background-color: var(--bg-input);\r\n    border-radius: var(--radius-md);\r\n  }\r\n\r\n  .mat-mdc-form-field-label {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  .mat-mdc-form-field-input {\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  .mat-mdc-form-field-icon-suffix {\r\n    color: var(--text-secondary);\r\n  }\r\n\r\n  &.mat-focused {\r\n    .mat-mdc-form-field-label {\r\n      color: var(--accent-green);\r\n    }\r\n  }\r\n\r\n  // Compact form field wrapper\r\n  .mat-mdc-form-field-wrapper {\r\n    padding-bottom: 0;\r\n  }\r\n\r\n  .mat-mdc-form-field-subscript-wrapper {\r\n    margin-top: 4px;\r\n    min-height: 16px;\r\n    font-size: var(--font-size-xs);\r\n  }\r\n}\r\n\r\n/* Snackbar Styles */\r\n::ng-deep .error-snackbar {\r\n  background-color: var(--error) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n::ng-deep .success-snackbar {\r\n  background-color: var(--success) !important;\r\n  color: var(--text-primary) !important;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .reset-card {\r\n    .auth-header {\r\n      padding: var(--spacing-sm) var(--spacing-md) var(--spacing-xs);\r\n    }\r\n\r\n    .auth-form {\r\n      padding: var(--spacing-sm) var(--spacing-md);\r\n    }\r\n\r\n    .auth-footer {\r\n      padding: var(--spacing-sm) var(--spacing-md);\r\n    }\r\n\r\n    .full-width {\r\n      margin-bottom: var(--spacing-sm);\r\n    }\r\n\r\n    .password-strength {\r\n      margin-bottom: var(--spacing-md);\r\n      padding: var(--spacing-sm);\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .reset-card {\r\n    margin: var(--spacing-xs);\r\n    max-width: calc(100vw - 1rem);\r\n\r\n    .auth-header {\r\n      padding: var(--spacing-xs) var(--spacing-sm) var(--spacing-xs);\r\n    }\r\n\r\n    .auth-form {\r\n      padding: var(--spacing-xs) var(--spacing-sm);\r\n    }\r\n\r\n    .auth-footer {\r\n      padding: var(--spacing-xs) var(--spacing-sm);\r\n    }\r\n\r\n    .auth-logo {\r\n      width: 45px;\r\n      height: 45px;\r\n      margin-bottom: var(--spacing-xs);\r\n    }\r\n\r\n    .auth-title {\r\n      font-size: var(--font-size-lg);\r\n      margin-bottom: 4px;\r\n    }\r\n\r\n    .auth-subtitle {\r\n      font-size: var(--font-size-xs);\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    .full-width {\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .reset-button {\r\n      height: 40px;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .password-strength {\r\n      margin-top: 8px;\r\n      margin-bottom: var(--spacing-sm);\r\n      padding: var(--spacing-xs);\r\n\r\n      h4 {\r\n        font-size: 11px;\r\n        margin-bottom: 6px;\r\n      }\r\n    }\r\n\r\n    .strength-requirement {\r\n      margin-bottom: 4px;\r\n\r\n      .requirement-icon {\r\n        width: 14px;\r\n        height: 14px;\r\n        font-size: 10px;\r\n        margin-right: 6px;\r\n      }\r\n\r\n      .requirement-text {\r\n        font-size: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* Focus and Hover States */\r\n.mat-mdc-button:focus {\r\n  outline: 2px solid var(--accent-green);\r\n  outline-offset: 2px;\r\n}\r\n\r\n.mat-mdc-form-field:focus-within {\r\n  .mat-mdc-text-field-wrapper {\r\n    border: 2px solid var(--accent-green);\r\n  }\r\n}\r\n\r\n/* Loading State Animation */\r\n.button-spinner {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Form validation visual feedback */\r\n.mat-mdc-form-field.ng-invalid.ng-touched {\r\n  .mat-mdc-text-field-wrapper {\r\n    border: 1px solid var(--error);\r\n  }\r\n}\r\n\r\n.mat-mdc-form-field.ng-valid.ng-touched {\r\n  .mat-mdc-text-field-wrapper {\r\n    border: 1px solid var(--success);\r\n  }\r\n}\r\n\r\n/* Password strength animation */\r\n.strength-requirement {\r\n  animation: fadeInUp 0.3s ease-in-out;\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Success animation for met requirements */\r\n.requirement-icon.met {\r\n  animation: checkmark 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes checkmark {\r\n  0% {\r\n    transform: scale(0.8);\r\n    opacity: 0.5;\r\n  }\r\n  50% {\r\n    transform: scale(1.1);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n"], "mappings": ";AAEA,CAAA;AACE,aAAA;;AAGF,CAAA;AACE,SAAA;AACA,iBAAA,IAAA;;AAGF,CAAA;AACE,UAAA;AACA,aAAA,IAAA;AACA,eAAA;AACA,kBAAA;AACA,iBAAA,IAAA;AACA,iBAAA,IAAA;AACA,YAAA;;AAEA,CATF,YASE;AACE,WAAA;;AAGF,CAbF,aAaE,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,YAAA;;AAGF,CArBF,aAqBE,CAAA;AACE,cAAA;AACA,WAAA;;AAIJ,CAAA;AACE,YAAA;AAEA,aAAA,UAAA,IAAA,EAAA;;AAIF,CAAA;AACE,cAAA,IAAA;AACA,iBAAA,IAAA;AACA,WAAA,IAAA;AACA,cAAA,IAAA;AACA,iBAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;;AAEA,CARF,kBAQE;AACE,aAAA,IAAA;AACA,SAAA,IAAA;AACA,iBAAA,IAAA;AACA,eAAA;;AAIJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,iBAAA,IAAA;;AAEA,CALF,oBAKE;AACE,iBAAA;;AAGF,CATF,qBASE,CAAA;AACE,SAAA;AACA,UAAA;AACA,gBAAA,IAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,aAAA;AACA,eAAA;AACA,cAAA,IAAA,IAAA;;AAEA,CArBJ,qBAqBI,CAZF,gBAYE,CAAA;AACE,oBAAA,IAAA;AACA,SAAA;AACA,aAAA,MAAA;;AAGF,CA3BJ,qBA2BI,CAlBF,gBAkBE,CAAA;AACE,oBAAA,IAAA;AACA,SAAA;AACA,aAAA,MAAA;AACA,WAAA;;AAIJ,CAnCF,qBAmCE,CAAA;AACE,aAAA,IAAA;AACA,cAAA,MAAA,IAAA;;AAEA,CAvCJ,qBAuCI,CAJF,gBAIE,CAlBA;AAmBE,SAAA,IAAA;AACA,eAAA;;AAGF,CA5CJ,qBA4CI,CATF,gBASE,CAjBA;AAkBE,SAAA,IAAA;;AAMN,CAAA;AACE,cAAA;AACA,eAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA,IAAA;AACA,SAAA,IAAA;AACA,kBAAA;AACA,aAAA,IAAA;AACA,UAAA,EAAA;;AAEA,CATF,WASE;AACE,SAAA,IAAA;AACA,cAAA;;AAKJ,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA;IAAA;MAAA,MAAA;MAAA,IAAA,gBAAA,EAAA;MAAA,IAAA,sBAAA;AAKA,iBAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,eAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAKA,UAAA,CA1JF,WA0JE,CAAA,mBAAA,CAAA;AACE,oBAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,UAAA,CA/JF,WA+JE,CALA,mBAKA,CAAA;AACE,SAAA,IAAA;;AAGF,UAAA,CAnKF,WAmKE,CATA,mBASA,CAAA;AACE,SAAA,IAAA;;AAGF,UAAA,CAvKF,WAuKE,CAbA,mBAaA,CAAA;AACE,SAAA,IAAA;;AAIA,UAAA,CA5KJ,WA4KI,CAlBF,kBAkBE,CAAA,YAAA,CAbF;AAcI,SAAA,IAAA;;AAKJ,UAAA,CAlLF,WAkLE,CAxBA,mBAwBA,CAAA;AACE,kBAAA;;AAGF,UAAA,CAtLF,WAsLE,CA5BA,mBA4BA,CAAA;AACE,cAAA;AACA,cAAA;AACA,aAAA,IAAA;;AAKJ,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAGF,UAAA,CAAA;AACE,oBAAA,IAAA;AACA,SAAA,IAAA;;AAIF,OAAA,CAAA,SAAA,EAAA;AAEI,GA3MJ,WA2MI,CAAA;AACE,aAAA,IAAA,cAAA,IAAA,cAAA,IAAA;;AAGF,GA/MJ,WA+MI,CAAA;AACE,aAAA,IAAA,cAAA,IAAA;;AAGF,GAnNJ,WAmNI,CAAA;AACE,aAAA,IAAA,cAAA,IAAA;;AAGF,GAvNJ,WAuNI,CAnNJ;AAoNM,mBAAA,IAAA;;AAGF,GA3NJ,WA2NI,CAhLJ;AAiLM,mBAAA,IAAA;AACA,aAAA,IAAA;;;AAKN,OAAA,CAAA,SAAA,EAAA;AACE,GAnOF;AAoOI,YAAA,IAAA;AACA,eAAA,KAAA,MAAA,EAAA;;AAEA,GAvOJ,WAuOI,CA5BA;AA6BE,aAAA,IAAA,cAAA,IAAA,cAAA,IAAA;;AAGF,GA3OJ,WA2OI,CA5BA;AA6BE,aAAA,IAAA,cAAA,IAAA;;AAGF,GA/OJ,WA+OI,CA5BA;AA6BE,aAAA,IAAA,cAAA,IAAA;;AAGF,GAnPJ,WAmPI,CAAA;AACE,WAAA;AACA,YAAA;AACA,mBAAA,IAAA;;AAGF,GAzPJ,WAyPI,CAAA;AACE,eAAA,IAAA;AACA,mBAAA;;AAGF,GA9PJ,WA8PI,CAAA;AACE,eAAA,IAAA;AACA,mBAAA;;AAGF,GAnQJ,WAmQI,CA/PJ;AAgQM,mBAAA;;AAGF,GAvQJ,WAuQI,CA9PJ;AA+PM,YAAA;AACA,mBAAA;;AAGF,GA5QJ,WA4QI,CAjOJ;AAkOM,gBAAA;AACA,mBAAA,IAAA;AACA,aAAA,IAAA;;AAEA,GAjRN,WAiRM,CAtON,kBAsOM;AACE,eAAA;AACA,mBAAA;;AAIJ,GAvRJ,WAuRI,CA5NJ;AA6NM,mBAAA;;AAEA,GA1RN,WA0RM,CA/NN,qBA+NM,CAtNJ;AAuNM,WAAA;AACA,YAAA;AACA,eAAA;AACA,kBAAA;;AAGF,GAjSN,WAiSM,CAtON,qBAsOM,CAnMJ;AAoMM,eAAA;;;AAOR,CAAA,cAAA;AACE,WAAA,IAAA,MAAA,IAAA;AACA,kBAAA;;AAIA,CArJA,kBAqJA,cAAA,CArJA;AAsJE,UAAA,IAAA,MAAA,IAAA;;AAKJ,CAjRA;AAkRE,aAAA,KAAA,GAAA,OAAA;;AAGF,WAHE;AAIA;AACE,eAAA,OAAA;;AAEF;AACE,eAAA,OAAA;;;AAMF,CA1KA,kBA0KA,CAAA,UAAA,CAAA,WAAA,CA1KA;AA2KE,UAAA,IAAA,MAAA,IAAA;;AAKF,CAhLA,kBAgLA,CAAA,QAAA,CANA,WAMA,CAhLA;AAiLE,UAAA,IAAA,MAAA,IAAA;;AAKJ,CArRA;AAsRE,aAAA,SAAA,KAAA;;AAGF,WAHE;AAIA;AACE,aAAA;AACA,eAAA,WAAA;;AAEF;AACE,aAAA;AACA,eAAA,WAAA;;;AAKJ,CA5RE,gBA4RF,CAhRI;AAiRF,aAAA,UAAA,KAAA;;AAGF,WAHE;AAIA;AACE,eAAA,MAAA;AACA,aAAA;;AAEF;AACE,eAAA,MAAA;;AAEF;AACE,eAAA,MAAA;AACA,aAAA;;;", "names": []}
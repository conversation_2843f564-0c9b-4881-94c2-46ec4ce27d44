﻿using Microsoft.AspNetCore.Identity;

namespace ChattrixBackend.Core.Entities.UserManagement.UserModel {
    public class ApplicationUser : IdentityUser {
        public string FullName { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public string ProfilePictureUrl { get; set; } = string.Empty;

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        // Chat-related properties
        public DateTime LastSeen { get; set; } = DateTime.UtcNow;
        public bool IsOnline { get; set; } = false;
        public string? CurrentConnectionId { get; set; }

        // Navigation properties for chat functionality
        public virtual ICollection<ConversationParticipant> ConversationParticipants { get; set; } = new List<ConversationParticipant>();
        public virtual ICollection<Message> SentMessages { get; set; } = new List<Message>();
        public virtual ICollection<MessageStatus> MessageStatuses { get; set; } = new List<MessageStatus>();
        public virtual ICollection<UserPresence> PresenceHistory { get; set; } = new List<UserPresence>();
    }
}

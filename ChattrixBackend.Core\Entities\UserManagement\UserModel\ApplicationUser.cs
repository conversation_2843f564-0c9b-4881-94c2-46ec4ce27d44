﻿using Microsoft.AspNetCore.Identity;

namespace ChattrixBackend.Core.Entities.UserManagement.UserModel {
    public class ApplicationUser : IdentityUser {
        public string FullName { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public string ProfilePictureUrl { get; set; } = string.Empty;

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;



    }
}
